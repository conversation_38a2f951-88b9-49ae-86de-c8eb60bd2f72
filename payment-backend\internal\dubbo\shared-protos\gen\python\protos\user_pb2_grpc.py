# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from protos import user_pb2 as protos_dot_user__pb2

GRPC_GENERATED_VERSION = '1.73.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in protos/user_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class UserServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetEmailVerifyCode = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/GetEmailVerifyCode',
                request_serializer=protos_dot_user__pb2.GetEmailVerifyCodeRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.GetEmailVerifyCodeResponse.FromString,
                _registered_method=True)
        self.VerifyEmailCode = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/VerifyEmailCode',
                request_serializer=protos_dot_user__pb2.CheckMailCodeRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.VerifyEmailCodeResponse.FromString,
                _registered_method=True)
        self.CreateUser = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/CreateUser',
                request_serializer=protos_dot_user__pb2.CreateUserRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.Login = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/Login',
                request_serializer=protos_dot_user__pb2.LoginRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.LoginWithCode = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/LoginWithCode',
                request_serializer=protos_dot_user__pb2.CheckMailCodeRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.RefreshToken = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/RefreshToken',
                request_serializer=protos_dot_user__pb2.Empty.SerializeToString,
                response_deserializer=protos_dot_user__pb2.LoginResponse.FromString,
                _registered_method=True)
        self.GetProfile = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/GetProfile',
                request_serializer=protos_dot_user__pb2.GetProfileRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.UserProfile.FromString,
                _registered_method=True)
        self.UpdateProfile = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/UpdateProfile',
                request_serializer=protos_dot_user__pb2.UpdateProfileRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.UserProfile.FromString,
                _registered_method=True)
        self.ResetPassword = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/ResetPassword',
                request_serializer=protos_dot_user__pb2.ResetPasswordRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.UpdateSystemPlan = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/UpdateSystemPlan',
                request_serializer=protos_dot_user__pb2.SystemPlan.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.GetSystemPlans = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/GetSystemPlans',
                request_serializer=protos_dot_user__pb2.Empty.SerializeToString,
                response_deserializer=protos_dot_user__pb2.SystemPlans.FromString,
                _registered_method=True)
        self.DeleteSystemPlan = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/DeleteSystemPlan',
                request_serializer=protos_dot_user__pb2.DeleteSystemPlanRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.UpdateSystemFeaturePoints = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/UpdateSystemFeaturePoints',
                request_serializer=protos_dot_user__pb2.SystemFeaturePoints.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Uint32Id.FromString,
                _registered_method=True)
        self.GetSystemFeaturePoints = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/GetSystemFeaturePoints',
                request_serializer=protos_dot_user__pb2.Empty.SerializeToString,
                response_deserializer=protos_dot_user__pb2.GetSystemFeaturePointsResponse.FromString,
                _registered_method=True)
        self.DeleteSystemFeaturePoints = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/DeleteSystemFeaturePoints',
                request_serializer=protos_dot_user__pb2.DeleteSystemFeaturePointsRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.GetPlan = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/GetPlan',
                request_serializer=protos_dot_user__pb2.Empty.SerializeToString,
                response_deserializer=protos_dot_user__pb2.UserPlan.FromString,
                _registered_method=True)
        self.ReducePlan = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/ReducePlan',
                request_serializer=protos_dot_user__pb2.ReduceBenefitRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.BenefitOrder.FromString,
                _registered_method=True)
        self.RollbackHalfPointBenefit = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/RollbackHalfPointBenefit',
                request_serializer=protos_dot_user__pb2.BenefitOrder.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.BenefitRecord = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/BenefitRecord',
                request_serializer=protos_dot_user__pb2.PageRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.BenefitRecordResponse.FromString,
                _registered_method=True)
        self.ChargePoints = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/ChargePoints',
                request_serializer=protos_dot_user__pb2.ChargeRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.Delete = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/Delete',
                request_serializer=protos_dot_user__pb2.Empty.SerializeToString,
                response_deserializer=protos_dot_user__pb2.Empty.FromString,
                _registered_method=True)
        self.Search = channel.unary_unary(
                '/com.aibook.user.grpc.UserService/Search',
                request_serializer=protos_dot_user__pb2.SearchRequest.SerializeToString,
                response_deserializer=protos_dot_user__pb2.SearchResponse.FromString,
                _registered_method=True)


class UserServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetEmailVerifyCode(self, request, context):
        """获取邮箱验证码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VerifyEmailCode(self, request, context):
        """校验邮箱验证码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateUser(self, request, context):
        """创建用户
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Login(self, request, context):
        """通过邮箱和密码登录
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoginWithCode(self, request, context):
        """通过邮箱和验证码登录
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RefreshToken(self, request, context):
        """刷新 token （需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProfile(self, request, context):
        """获取用户信息（需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateProfile(self, request, context):
        """更新用户信息（需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetPassword(self, request, context):
        """找回密码
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateSystemPlan(self, request, context):
        """创建或修改系统套餐(套餐id为0表示创建) （非终端访问接口）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemPlans(self, request, context):
        """用户请求，则根据国家代码筛选；管理平台请求，返回所有国家套餐。 (管理平台调用)
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSystemPlan(self, request, context):
        """删除系统套餐（非终端访问接口）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateSystemFeaturePoints(self, request, context):
        """创建或修改功能积分价格记录（id为0表示创建）（非终端访问接口）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemFeaturePoints(self, request, context):
        """查询功能积分价格记录，返回所有记录。（非终端访问接口）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSystemFeaturePoints(self, request, context):
        """删除功能积分价格记录（非终端访问接口）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlan(self, request, context):
        """获取用户套餐信息（需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReducePlan(self, request, context):
        """权益扣减（需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RollbackHalfPointBenefit(self, request, context):
        """回退一半积分权益 - ai 模块调用
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def BenefitRecord(self, request, context):
        """权益使用记录
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ChargePoints(self, request, context):
        """积分充值
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Delete(self, request, context):
        """注销用户（需认证 token）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Search(self, request, context):
        """查询用户， 条件为空时，则列举全部用户；不为空时，多个条件为且的关系给结果
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_UserServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetEmailVerifyCode': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEmailVerifyCode,
                    request_deserializer=protos_dot_user__pb2.GetEmailVerifyCodeRequest.FromString,
                    response_serializer=protos_dot_user__pb2.GetEmailVerifyCodeResponse.SerializeToString,
            ),
            'VerifyEmailCode': grpc.unary_unary_rpc_method_handler(
                    servicer.VerifyEmailCode,
                    request_deserializer=protos_dot_user__pb2.CheckMailCodeRequest.FromString,
                    response_serializer=protos_dot_user__pb2.VerifyEmailCodeResponse.SerializeToString,
            ),
            'CreateUser': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateUser,
                    request_deserializer=protos_dot_user__pb2.CreateUserRequest.FromString,
                    response_serializer=protos_dot_user__pb2.LoginResponse.SerializeToString,
            ),
            'Login': grpc.unary_unary_rpc_method_handler(
                    servicer.Login,
                    request_deserializer=protos_dot_user__pb2.LoginRequest.FromString,
                    response_serializer=protos_dot_user__pb2.LoginResponse.SerializeToString,
            ),
            'LoginWithCode': grpc.unary_unary_rpc_method_handler(
                    servicer.LoginWithCode,
                    request_deserializer=protos_dot_user__pb2.CheckMailCodeRequest.FromString,
                    response_serializer=protos_dot_user__pb2.LoginResponse.SerializeToString,
            ),
            'RefreshToken': grpc.unary_unary_rpc_method_handler(
                    servicer.RefreshToken,
                    request_deserializer=protos_dot_user__pb2.Empty.FromString,
                    response_serializer=protos_dot_user__pb2.LoginResponse.SerializeToString,
            ),
            'GetProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfile,
                    request_deserializer=protos_dot_user__pb2.GetProfileRequest.FromString,
                    response_serializer=protos_dot_user__pb2.UserProfile.SerializeToString,
            ),
            'UpdateProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateProfile,
                    request_deserializer=protos_dot_user__pb2.UpdateProfileRequest.FromString,
                    response_serializer=protos_dot_user__pb2.UserProfile.SerializeToString,
            ),
            'ResetPassword': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetPassword,
                    request_deserializer=protos_dot_user__pb2.ResetPasswordRequest.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'UpdateSystemPlan': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateSystemPlan,
                    request_deserializer=protos_dot_user__pb2.SystemPlan.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'GetSystemPlans': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemPlans,
                    request_deserializer=protos_dot_user__pb2.Empty.FromString,
                    response_serializer=protos_dot_user__pb2.SystemPlans.SerializeToString,
            ),
            'DeleteSystemPlan': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSystemPlan,
                    request_deserializer=protos_dot_user__pb2.DeleteSystemPlanRequest.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'UpdateSystemFeaturePoints': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateSystemFeaturePoints,
                    request_deserializer=protos_dot_user__pb2.SystemFeaturePoints.FromString,
                    response_serializer=protos_dot_user__pb2.Uint32Id.SerializeToString,
            ),
            'GetSystemFeaturePoints': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemFeaturePoints,
                    request_deserializer=protos_dot_user__pb2.Empty.FromString,
                    response_serializer=protos_dot_user__pb2.GetSystemFeaturePointsResponse.SerializeToString,
            ),
            'DeleteSystemFeaturePoints': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSystemFeaturePoints,
                    request_deserializer=protos_dot_user__pb2.DeleteSystemFeaturePointsRequest.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'GetPlan': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlan,
                    request_deserializer=protos_dot_user__pb2.Empty.FromString,
                    response_serializer=protos_dot_user__pb2.UserPlan.SerializeToString,
            ),
            'ReducePlan': grpc.unary_unary_rpc_method_handler(
                    servicer.ReducePlan,
                    request_deserializer=protos_dot_user__pb2.ReduceBenefitRequest.FromString,
                    response_serializer=protos_dot_user__pb2.BenefitOrder.SerializeToString,
            ),
            'RollbackHalfPointBenefit': grpc.unary_unary_rpc_method_handler(
                    servicer.RollbackHalfPointBenefit,
                    request_deserializer=protos_dot_user__pb2.BenefitOrder.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'BenefitRecord': grpc.unary_unary_rpc_method_handler(
                    servicer.BenefitRecord,
                    request_deserializer=protos_dot_user__pb2.PageRequest.FromString,
                    response_serializer=protos_dot_user__pb2.BenefitRecordResponse.SerializeToString,
            ),
            'ChargePoints': grpc.unary_unary_rpc_method_handler(
                    servicer.ChargePoints,
                    request_deserializer=protos_dot_user__pb2.ChargeRequest.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'Delete': grpc.unary_unary_rpc_method_handler(
                    servicer.Delete,
                    request_deserializer=protos_dot_user__pb2.Empty.FromString,
                    response_serializer=protos_dot_user__pb2.Empty.SerializeToString,
            ),
            'Search': grpc.unary_unary_rpc_method_handler(
                    servicer.Search,
                    request_deserializer=protos_dot_user__pb2.SearchRequest.FromString,
                    response_serializer=protos_dot_user__pb2.SearchResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'com.aibook.user.grpc.UserService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('com.aibook.user.grpc.UserService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class UserService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetEmailVerifyCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/GetEmailVerifyCode',
            protos_dot_user__pb2.GetEmailVerifyCodeRequest.SerializeToString,
            protos_dot_user__pb2.GetEmailVerifyCodeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def VerifyEmailCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/VerifyEmailCode',
            protos_dot_user__pb2.CheckMailCodeRequest.SerializeToString,
            protos_dot_user__pb2.VerifyEmailCodeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/CreateUser',
            protos_dot_user__pb2.CreateUserRequest.SerializeToString,
            protos_dot_user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Login(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/Login',
            protos_dot_user__pb2.LoginRequest.SerializeToString,
            protos_dot_user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LoginWithCode(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/LoginWithCode',
            protos_dot_user__pb2.CheckMailCodeRequest.SerializeToString,
            protos_dot_user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RefreshToken(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/RefreshToken',
            protos_dot_user__pb2.Empty.SerializeToString,
            protos_dot_user__pb2.LoginResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/GetProfile',
            protos_dot_user__pb2.GetProfileRequest.SerializeToString,
            protos_dot_user__pb2.UserProfile.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/UpdateProfile',
            protos_dot_user__pb2.UpdateProfileRequest.SerializeToString,
            protos_dot_user__pb2.UserProfile.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResetPassword(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/ResetPassword',
            protos_dot_user__pb2.ResetPasswordRequest.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateSystemPlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/UpdateSystemPlan',
            protos_dot_user__pb2.SystemPlan.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSystemPlans(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/GetSystemPlans',
            protos_dot_user__pb2.Empty.SerializeToString,
            protos_dot_user__pb2.SystemPlans.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSystemPlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/DeleteSystemPlan',
            protos_dot_user__pb2.DeleteSystemPlanRequest.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateSystemFeaturePoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/UpdateSystemFeaturePoints',
            protos_dot_user__pb2.SystemFeaturePoints.SerializeToString,
            protos_dot_user__pb2.Uint32Id.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSystemFeaturePoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/GetSystemFeaturePoints',
            protos_dot_user__pb2.Empty.SerializeToString,
            protos_dot_user__pb2.GetSystemFeaturePointsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSystemFeaturePoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/DeleteSystemFeaturePoints',
            protos_dot_user__pb2.DeleteSystemFeaturePointsRequest.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/GetPlan',
            protos_dot_user__pb2.Empty.SerializeToString,
            protos_dot_user__pb2.UserPlan.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ReducePlan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/ReducePlan',
            protos_dot_user__pb2.ReduceBenefitRequest.SerializeToString,
            protos_dot_user__pb2.BenefitOrder.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RollbackHalfPointBenefit(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/RollbackHalfPointBenefit',
            protos_dot_user__pb2.BenefitOrder.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def BenefitRecord(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/BenefitRecord',
            protos_dot_user__pb2.PageRequest.SerializeToString,
            protos_dot_user__pb2.BenefitRecordResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ChargePoints(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/ChargePoints',
            protos_dot_user__pb2.ChargeRequest.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Delete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/Delete',
            protos_dot_user__pb2.Empty.SerializeToString,
            protos_dot_user__pb2.Empty.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Search(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/com.aibook.user.grpc.UserService/Search',
            protos_dot_user__pb2.SearchRequest.SerializeToString,
            protos_dot_user__pb2.SearchResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
