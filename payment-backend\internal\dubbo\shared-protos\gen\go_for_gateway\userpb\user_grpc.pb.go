// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v6.31.1
// source: protos/user.proto

package userpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	UserService_GetEmailVerifyCode_FullMethodName        = "/com.aibook.user.grpc.UserService/GetEmailVerifyCode"
	UserService_VerifyEmailCode_FullMethodName           = "/com.aibook.user.grpc.UserService/VerifyEmailCode"
	UserService_CreateUser_FullMethodName                = "/com.aibook.user.grpc.UserService/CreateUser"
	UserService_Login_FullMethodName                     = "/com.aibook.user.grpc.UserService/Login"
	UserService_LoginWithCode_FullMethodName             = "/com.aibook.user.grpc.UserService/LoginWithCode"
	UserService_RefreshToken_FullMethodName              = "/com.aibook.user.grpc.UserService/RefreshToken"
	UserService_GetProfile_FullMethodName                = "/com.aibook.user.grpc.UserService/GetProfile"
	UserService_UpdateProfile_FullMethodName             = "/com.aibook.user.grpc.UserService/UpdateProfile"
	UserService_ResetPassword_FullMethodName             = "/com.aibook.user.grpc.UserService/ResetPassword"
	UserService_UpdateSystemPlan_FullMethodName          = "/com.aibook.user.grpc.UserService/UpdateSystemPlan"
	UserService_GetSystemPlans_FullMethodName            = "/com.aibook.user.grpc.UserService/GetSystemPlans"
	UserService_DeleteSystemPlan_FullMethodName          = "/com.aibook.user.grpc.UserService/DeleteSystemPlan"
	UserService_UpdateSystemFeaturePoints_FullMethodName = "/com.aibook.user.grpc.UserService/UpdateSystemFeaturePoints"
	UserService_GetSystemFeaturePoints_FullMethodName    = "/com.aibook.user.grpc.UserService/GetSystemFeaturePoints"
	UserService_DeleteSystemFeaturePoints_FullMethodName = "/com.aibook.user.grpc.UserService/DeleteSystemFeaturePoints"
	UserService_GetPlan_FullMethodName                   = "/com.aibook.user.grpc.UserService/GetPlan"
	UserService_ReducePlan_FullMethodName                = "/com.aibook.user.grpc.UserService/ReducePlan"
	UserService_RollbackHalfPointBenefit_FullMethodName  = "/com.aibook.user.grpc.UserService/RollbackHalfPointBenefit"
	UserService_BenefitRecord_FullMethodName             = "/com.aibook.user.grpc.UserService/BenefitRecord"
	UserService_ChargePoints_FullMethodName              = "/com.aibook.user.grpc.UserService/ChargePoints"
	UserService_Delete_FullMethodName                    = "/com.aibook.user.grpc.UserService/Delete"
	UserService_Search_FullMethodName                    = "/com.aibook.user.grpc.UserService/Search"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserServiceClient interface {
	// 获取邮箱验证码
	GetEmailVerifyCode(ctx context.Context, in *GetEmailVerifyCodeRequest, opts ...grpc.CallOption) (*GetEmailVerifyCodeResponse, error)
	// 校验邮箱验证码
	VerifyEmailCode(ctx context.Context, in *CheckMailCodeRequest, opts ...grpc.CallOption) (*VerifyEmailCodeResponse, error)
	// 创建用户
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*LoginResponse, error)
	// 通过邮箱和密码登录
	Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginResponse, error)
	// 通过邮箱和验证码登录
	LoginWithCode(ctx context.Context, in *CheckMailCodeRequest, opts ...grpc.CallOption) (*LoginResponse, error)
	// 刷新 token （需认证 token）
	RefreshToken(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LoginResponse, error)
	// 获取用户信息（需认证 token）
	GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*UserProfile, error)
	// 更新用户信息（需认证 token）
	UpdateProfile(ctx context.Context, in *UpdateProfileRequest, opts ...grpc.CallOption) (*UserProfile, error)
	// 找回密码
	ResetPassword(ctx context.Context, in *ResetPasswordRequest, opts ...grpc.CallOption) (*Empty, error)
	// 创建或修改系统套餐(套餐id为0表示创建) （非终端访问接口）
	UpdateSystemPlan(ctx context.Context, in *SystemPlan, opts ...grpc.CallOption) (*Empty, error)
	// 用户请求，则根据国家代码筛选；管理平台请求，返回所有国家套餐。 (管理平台调用)
	GetSystemPlans(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SystemPlans, error)
	// 删除系统套餐（非终端访问接口）
	DeleteSystemPlan(ctx context.Context, in *DeleteSystemPlanRequest, opts ...grpc.CallOption) (*Empty, error)
	// 创建或修改功能积分价格记录（id为0表示创建）（非终端访问接口）
	UpdateSystemFeaturePoints(ctx context.Context, in *SystemFeaturePoints, opts ...grpc.CallOption) (*Uint32Id, error)
	// 查询功能积分价格记录，返回所有记录。（非终端访问接口）
	GetSystemFeaturePoints(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetSystemFeaturePointsResponse, error)
	// 删除功能积分价格记录（非终端访问接口）
	DeleteSystemFeaturePoints(ctx context.Context, in *DeleteSystemFeaturePointsRequest, opts ...grpc.CallOption) (*Empty, error)
	// 获取用户套餐信息（需认证 token）
	GetPlan(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*UserPlan, error)
	// 权益扣减（需认证 token）
	ReducePlan(ctx context.Context, in *ReduceBenefitRequest, opts ...grpc.CallOption) (*BenefitOrder, error)
	// 回退一半积分权益 - ai 模块调用
	RollbackHalfPointBenefit(ctx context.Context, in *BenefitOrder, opts ...grpc.CallOption) (*Empty, error)
	// 权益使用记录
	BenefitRecord(ctx context.Context, in *PageRequest, opts ...grpc.CallOption) (*BenefitRecordResponse, error)
	// 积分充值
	ChargePoints(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*Empty, error)
	// 注销用户（需认证 token）
	Delete(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 查询用户， 条件为空时，则列举全部用户；不为空时，多个条件为且的关系给结果
	Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) GetEmailVerifyCode(ctx context.Context, in *GetEmailVerifyCodeRequest, opts ...grpc.CallOption) (*GetEmailVerifyCodeResponse, error) {
	out := new(GetEmailVerifyCodeResponse)
	err := c.cc.Invoke(ctx, UserService_GetEmailVerifyCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) VerifyEmailCode(ctx context.Context, in *CheckMailCodeRequest, opts ...grpc.CallOption) (*VerifyEmailCodeResponse, error) {
	out := new(VerifyEmailCodeResponse)
	err := c.cc.Invoke(ctx, UserService_VerifyEmailCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*LoginResponse, error) {
	out := new(LoginResponse)
	err := c.cc.Invoke(ctx, UserService_CreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginResponse, error) {
	out := new(LoginResponse)
	err := c.cc.Invoke(ctx, UserService_Login_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginWithCode(ctx context.Context, in *CheckMailCodeRequest, opts ...grpc.CallOption) (*LoginResponse, error) {
	out := new(LoginResponse)
	err := c.cc.Invoke(ctx, UserService_LoginWithCode_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RefreshToken(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LoginResponse, error) {
	out := new(LoginResponse)
	err := c.cc.Invoke(ctx, UserService_RefreshToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetProfile(ctx context.Context, in *GetProfileRequest, opts ...grpc.CallOption) (*UserProfile, error) {
	out := new(UserProfile)
	err := c.cc.Invoke(ctx, UserService_GetProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateProfile(ctx context.Context, in *UpdateProfileRequest, opts ...grpc.CallOption) (*UserProfile, error) {
	out := new(UserProfile)
	err := c.cc.Invoke(ctx, UserService_UpdateProfile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ResetPassword(ctx context.Context, in *ResetPasswordRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_ResetPassword_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateSystemPlan(ctx context.Context, in *SystemPlan, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_UpdateSystemPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetSystemPlans(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*SystemPlans, error) {
	out := new(SystemPlans)
	err := c.cc.Invoke(ctx, UserService_GetSystemPlans_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteSystemPlan(ctx context.Context, in *DeleteSystemPlanRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_DeleteSystemPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateSystemFeaturePoints(ctx context.Context, in *SystemFeaturePoints, opts ...grpc.CallOption) (*Uint32Id, error) {
	out := new(Uint32Id)
	err := c.cc.Invoke(ctx, UserService_UpdateSystemFeaturePoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetSystemFeaturePoints(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*GetSystemFeaturePointsResponse, error) {
	out := new(GetSystemFeaturePointsResponse)
	err := c.cc.Invoke(ctx, UserService_GetSystemFeaturePoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) DeleteSystemFeaturePoints(ctx context.Context, in *DeleteSystemFeaturePointsRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_DeleteSystemFeaturePoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetPlan(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*UserPlan, error) {
	out := new(UserPlan)
	err := c.cc.Invoke(ctx, UserService_GetPlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ReducePlan(ctx context.Context, in *ReduceBenefitRequest, opts ...grpc.CallOption) (*BenefitOrder, error) {
	out := new(BenefitOrder)
	err := c.cc.Invoke(ctx, UserService_ReducePlan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RollbackHalfPointBenefit(ctx context.Context, in *BenefitOrder, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_RollbackHalfPointBenefit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) BenefitRecord(ctx context.Context, in *PageRequest, opts ...grpc.CallOption) (*BenefitRecordResponse, error) {
	out := new(BenefitRecordResponse)
	err := c.cc.Invoke(ctx, UserService_BenefitRecord_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) ChargePoints(ctx context.Context, in *ChargeRequest, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_ChargePoints_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) Delete(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserService_Delete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error) {
	out := new(SearchResponse)
	err := c.cc.Invoke(ctx, UserService_Search_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations must embed UnimplementedUserServiceServer
// for forward compatibility
type UserServiceServer interface {
	// 获取邮箱验证码
	GetEmailVerifyCode(context.Context, *GetEmailVerifyCodeRequest) (*GetEmailVerifyCodeResponse, error)
	// 校验邮箱验证码
	VerifyEmailCode(context.Context, *CheckMailCodeRequest) (*VerifyEmailCodeResponse, error)
	// 创建用户
	CreateUser(context.Context, *CreateUserRequest) (*LoginResponse, error)
	// 通过邮箱和密码登录
	Login(context.Context, *LoginRequest) (*LoginResponse, error)
	// 通过邮箱和验证码登录
	LoginWithCode(context.Context, *CheckMailCodeRequest) (*LoginResponse, error)
	// 刷新 token （需认证 token）
	RefreshToken(context.Context, *Empty) (*LoginResponse, error)
	// 获取用户信息（需认证 token）
	GetProfile(context.Context, *GetProfileRequest) (*UserProfile, error)
	// 更新用户信息（需认证 token）
	UpdateProfile(context.Context, *UpdateProfileRequest) (*UserProfile, error)
	// 找回密码
	ResetPassword(context.Context, *ResetPasswordRequest) (*Empty, error)
	// 创建或修改系统套餐(套餐id为0表示创建) （非终端访问接口）
	UpdateSystemPlan(context.Context, *SystemPlan) (*Empty, error)
	// 用户请求，则根据国家代码筛选；管理平台请求，返回所有国家套餐。 (管理平台调用)
	GetSystemPlans(context.Context, *Empty) (*SystemPlans, error)
	// 删除系统套餐（非终端访问接口）
	DeleteSystemPlan(context.Context, *DeleteSystemPlanRequest) (*Empty, error)
	// 创建或修改功能积分价格记录（id为0表示创建）（非终端访问接口）
	UpdateSystemFeaturePoints(context.Context, *SystemFeaturePoints) (*Uint32Id, error)
	// 查询功能积分价格记录，返回所有记录。（非终端访问接口）
	GetSystemFeaturePoints(context.Context, *Empty) (*GetSystemFeaturePointsResponse, error)
	// 删除功能积分价格记录（非终端访问接口）
	DeleteSystemFeaturePoints(context.Context, *DeleteSystemFeaturePointsRequest) (*Empty, error)
	// 获取用户套餐信息（需认证 token）
	GetPlan(context.Context, *Empty) (*UserPlan, error)
	// 权益扣减（需认证 token）
	ReducePlan(context.Context, *ReduceBenefitRequest) (*BenefitOrder, error)
	// 回退一半积分权益 - ai 模块调用
	RollbackHalfPointBenefit(context.Context, *BenefitOrder) (*Empty, error)
	// 权益使用记录
	BenefitRecord(context.Context, *PageRequest) (*BenefitRecordResponse, error)
	// 积分充值
	ChargePoints(context.Context, *ChargeRequest) (*Empty, error)
	// 注销用户（需认证 token）
	Delete(context.Context, *Empty) (*Empty, error)
	// 查询用户， 条件为空时，则列举全部用户；不为空时，多个条件为且的关系给结果
	Search(context.Context, *SearchRequest) (*SearchResponse, error)
	mustEmbedUnimplementedUserServiceServer()
}

// UnimplementedUserServiceServer must be embedded to have forward compatible implementations.
type UnimplementedUserServiceServer struct {
}

func (UnimplementedUserServiceServer) GetEmailVerifyCode(context.Context, *GetEmailVerifyCodeRequest) (*GetEmailVerifyCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEmailVerifyCode not implemented")
}
func (UnimplementedUserServiceServer) VerifyEmailCode(context.Context, *CheckMailCodeRequest) (*VerifyEmailCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyEmailCode not implemented")
}
func (UnimplementedUserServiceServer) CreateUser(context.Context, *CreateUserRequest) (*LoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedUserServiceServer) Login(context.Context, *LoginRequest) (*LoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedUserServiceServer) LoginWithCode(context.Context, *CheckMailCodeRequest) (*LoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginWithCode not implemented")
}
func (UnimplementedUserServiceServer) RefreshToken(context.Context, *Empty) (*LoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefreshToken not implemented")
}
func (UnimplementedUserServiceServer) GetProfile(context.Context, *GetProfileRequest) (*UserProfile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProfile not implemented")
}
func (UnimplementedUserServiceServer) UpdateProfile(context.Context, *UpdateProfileRequest) (*UserProfile, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProfile not implemented")
}
func (UnimplementedUserServiceServer) ResetPassword(context.Context, *ResetPasswordRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedUserServiceServer) UpdateSystemPlan(context.Context, *SystemPlan) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSystemPlan not implemented")
}
func (UnimplementedUserServiceServer) GetSystemPlans(context.Context, *Empty) (*SystemPlans, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemPlans not implemented")
}
func (UnimplementedUserServiceServer) DeleteSystemPlan(context.Context, *DeleteSystemPlanRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSystemPlan not implemented")
}
func (UnimplementedUserServiceServer) UpdateSystemFeaturePoints(context.Context, *SystemFeaturePoints) (*Uint32Id, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSystemFeaturePoints not implemented")
}
func (UnimplementedUserServiceServer) GetSystemFeaturePoints(context.Context, *Empty) (*GetSystemFeaturePointsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSystemFeaturePoints not implemented")
}
func (UnimplementedUserServiceServer) DeleteSystemFeaturePoints(context.Context, *DeleteSystemFeaturePointsRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSystemFeaturePoints not implemented")
}
func (UnimplementedUserServiceServer) GetPlan(context.Context, *Empty) (*UserPlan, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlan not implemented")
}
func (UnimplementedUserServiceServer) ReducePlan(context.Context, *ReduceBenefitRequest) (*BenefitOrder, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReducePlan not implemented")
}
func (UnimplementedUserServiceServer) RollbackHalfPointBenefit(context.Context, *BenefitOrder) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackHalfPointBenefit not implemented")
}
func (UnimplementedUserServiceServer) BenefitRecord(context.Context, *PageRequest) (*BenefitRecordResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BenefitRecord not implemented")
}
func (UnimplementedUserServiceServer) ChargePoints(context.Context, *ChargeRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargePoints not implemented")
}
func (UnimplementedUserServiceServer) Delete(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedUserServiceServer) Search(context.Context, *SearchRequest) (*SearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Search not implemented")
}
func (UnimplementedUserServiceServer) mustEmbedUnimplementedUserServiceServer() {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_GetEmailVerifyCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEmailVerifyCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetEmailVerifyCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetEmailVerifyCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetEmailVerifyCode(ctx, req.(*GetEmailVerifyCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_VerifyEmailCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMailCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).VerifyEmailCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_VerifyEmailCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).VerifyEmailCode(ctx, req.(*CheckMailCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).Login(ctx, req.(*LoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginWithCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMailCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginWithCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginWithCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginWithCode(ctx, req.(*CheckMailCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RefreshToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RefreshToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RefreshToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RefreshToken(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetProfile(ctx, req.(*GetProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateProfile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProfileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateProfile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateProfile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateProfile(ctx, req.(*UpdateProfileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPasswordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ResetPassword(ctx, req.(*ResetPasswordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateSystemPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SystemPlan)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateSystemPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateSystemPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateSystemPlan(ctx, req.(*SystemPlan))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetSystemPlans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetSystemPlans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetSystemPlans_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetSystemPlans(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteSystemPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSystemPlanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteSystemPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteSystemPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteSystemPlan(ctx, req.(*DeleteSystemPlanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateSystemFeaturePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SystemFeaturePoints)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateSystemFeaturePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateSystemFeaturePoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateSystemFeaturePoints(ctx, req.(*SystemFeaturePoints))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetSystemFeaturePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetSystemFeaturePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetSystemFeaturePoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetSystemFeaturePoints(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_DeleteSystemFeaturePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSystemFeaturePointsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).DeleteSystemFeaturePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_DeleteSystemFeaturePoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).DeleteSystemFeaturePoints(ctx, req.(*DeleteSystemFeaturePointsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetPlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetPlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetPlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetPlan(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ReducePlan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReduceBenefitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ReducePlan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ReducePlan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ReducePlan(ctx, req.(*ReduceBenefitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RollbackHalfPointBenefit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BenefitOrder)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RollbackHalfPointBenefit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RollbackHalfPointBenefit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RollbackHalfPointBenefit(ctx, req.(*BenefitOrder))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_BenefitRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).BenefitRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_BenefitRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).BenefitRecord(ctx, req.(*PageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_ChargePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChargeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).ChargePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_ChargePoints_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).ChargePoints(ctx, req.(*ChargeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).Delete(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_Search_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).Search(ctx, req.(*SearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.user.grpc.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEmailVerifyCode",
			Handler:    _UserService_GetEmailVerifyCode_Handler,
		},
		{
			MethodName: "VerifyEmailCode",
			Handler:    _UserService_VerifyEmailCode_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _UserService_CreateUser_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _UserService_Login_Handler,
		},
		{
			MethodName: "LoginWithCode",
			Handler:    _UserService_LoginWithCode_Handler,
		},
		{
			MethodName: "RefreshToken",
			Handler:    _UserService_RefreshToken_Handler,
		},
		{
			MethodName: "GetProfile",
			Handler:    _UserService_GetProfile_Handler,
		},
		{
			MethodName: "UpdateProfile",
			Handler:    _UserService_UpdateProfile_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _UserService_ResetPassword_Handler,
		},
		{
			MethodName: "UpdateSystemPlan",
			Handler:    _UserService_UpdateSystemPlan_Handler,
		},
		{
			MethodName: "GetSystemPlans",
			Handler:    _UserService_GetSystemPlans_Handler,
		},
		{
			MethodName: "DeleteSystemPlan",
			Handler:    _UserService_DeleteSystemPlan_Handler,
		},
		{
			MethodName: "UpdateSystemFeaturePoints",
			Handler:    _UserService_UpdateSystemFeaturePoints_Handler,
		},
		{
			MethodName: "GetSystemFeaturePoints",
			Handler:    _UserService_GetSystemFeaturePoints_Handler,
		},
		{
			MethodName: "DeleteSystemFeaturePoints",
			Handler:    _UserService_DeleteSystemFeaturePoints_Handler,
		},
		{
			MethodName: "GetPlan",
			Handler:    _UserService_GetPlan_Handler,
		},
		{
			MethodName: "ReducePlan",
			Handler:    _UserService_ReducePlan_Handler,
		},
		{
			MethodName: "RollbackHalfPointBenefit",
			Handler:    _UserService_RollbackHalfPointBenefit_Handler,
		},
		{
			MethodName: "BenefitRecord",
			Handler:    _UserService_BenefitRecord_Handler,
		},
		{
			MethodName: "ChargePoints",
			Handler:    _UserService_ChargePoints_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _UserService_Delete_Handler,
		},
		{
			MethodName: "Search",
			Handler:    _UserService_Search_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/user.proto",
}
