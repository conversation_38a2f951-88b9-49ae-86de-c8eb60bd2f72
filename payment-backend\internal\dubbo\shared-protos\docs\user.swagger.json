{"swagger": "2.0", "info": {"title": "protos/user.proto", "version": "version not set"}, "tags": [{"name": "UserService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/v1/user": {"delete": {"summary": "注销用户（需认证 token）", "operationId": "UserService_Delete", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}, "/api/v1/user/charge": {"post": {"summary": "积分充值", "operationId": "UserService_ChargePoints", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcChargeRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/create": {"post": {"summary": "创建用户", "operationId": "UserService_CreateUser", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcLoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcCreateUserRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/email_verify_code": {"get": {"summary": "获取邮箱验证码", "operationId": "UserService_GetEmailVerifyCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcGetEmailVerifyCodeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "email", "description": "邮箱", "in": "query", "required": false, "type": "string"}], "tags": ["UserService"]}}, "/api/v1/user/login": {"post": {"summary": "通过邮箱和密码登录", "operationId": "UserService_Login", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcLoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcLoginRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/login_with_code": {"post": {"summary": "通过邮箱和验证码登录", "operationId": "UserService_LoginWithCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcLoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcCheckMailCodeRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/plan": {"get": {"summary": "获取用户套餐信息（需认证 token）", "operationId": "UserService_GetPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcUserPlan"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}, "/api/v1/user/plan/record": {"get": {"summary": "权益使用记录", "operationId": "UserService_BenefitRecord", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBenefitRecordResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "description": "页码(从 1 开始)", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int32"}], "tags": ["UserService"]}}, "/api/v1/user/plan/reduce": {"post": {"summary": "权益扣减（需认证 token）", "operationId": "UserService_ReducePlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcBenefitOrder"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcReduceBenefitRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/plan/rollback": {"post": {"summary": "回退一半积分权益 - ai 模块调用", "operationId": "UserService_RollbackHalfPointBenefit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcBenefitOrder"}}], "tags": ["UserService"]}}, "/api/v1/user/profile": {"get": {"summary": "获取用户信息（需认证 token）", "operationId": "UserService_GetProfile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcUserProfile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}, "post": {"summary": "更新用户信息（需认证 token）", "operationId": "UserService_UpdateProfile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcUserProfile"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcUpdateProfileRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/refresh_token": {"post": {"summary": "刷新 token （需认证 token）", "operationId": "UserService_RefreshToken", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcLoginResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcEmpty"}}], "tags": ["UserService"]}}, "/api/v1/user/reset_password": {"post": {"summary": "找回密码", "operationId": "UserService_ResetPassword", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcResetPasswordRequest"}}], "tags": ["UserService"]}}, "/api/v1/user/search": {"get": {"summary": "查询用户， 条件为空时，则列举全部用户；不为空时，多个条件为且的关系给结果", "operationId": "UserService_Search", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcSearchResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "page", "description": "页码(从 1 开始)", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "pageSize", "description": "每页数量", "in": "query", "required": false, "type": "integer", "format": "int32"}, {"name": "searchByNickname", "description": "根据昵称查询", "in": "query", "required": false, "type": "string"}, {"name": "searchByEmail", "description": "根据邮箱查询", "in": "query", "required": false, "type": "string"}, {"name": "searchByDeviceId", "description": "根据设备序列号查询", "in": "query", "required": false, "type": "string"}, {"name": "searchByUserId", "description": "根据用户ID查询", "in": "query", "required": false, "type": "string", "format": "uint64"}], "tags": ["UserService"]}}, "/api/v1/user/sys/feature_points": {"get": {"summary": "查询功能积分价格记录，返回所有记录。（非终端访问接口）", "operationId": "UserService_GetSystemFeaturePoints", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcGetSystemFeaturePointsResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}, "delete": {"summary": "删除功能积分价格记录（非终端访问接口）", "operationId": "UserService_DeleteSystemFeaturePoints", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "id", "description": "记录id", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["UserService"]}, "put": {"summary": "创建或修改功能积分价格记录（id为0表示创建）（非终端访问接口）", "operationId": "UserService_UpdateSystemFeaturePoints", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcUint32Id"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcSystemFeaturePoints"}}], "tags": ["UserService"]}}, "/api/v1/user/sys/plan": {"delete": {"summary": "删除系统套餐（非终端访问接口）", "operationId": "UserService_DeleteSystemPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "planId", "description": "套餐id", "in": "query", "required": false, "type": "integer", "format": "int64"}], "tags": ["UserService"]}, "put": {"summary": "创建或修改系统套餐(套餐id为0表示创建) （非终端访问接口）", "operationId": "UserService_UpdateSystemPlan", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcSystemPlan"}}], "tags": ["UserService"]}}, "/api/v1/user/sys/plans": {"get": {"summary": "用户请求，则根据国家代码筛选；管理平台请求，返回所有国家套餐。 (管理平台调用)", "operationId": "UserService_GetSystemPlans", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcSystemPlans"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "tags": ["UserService"]}}, "/api/v1/user/verify_email_code": {"post": {"summary": "校验邮箱验证码", "operationId": "UserService_VerifyEmailCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/grpcVerifyEmailCodeResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/grpcCheckMailCodeRequest"}}], "tags": ["UserService"]}}}, "definitions": {"grpcBenefitOrder": {"type": "object", "properties": {"orderId": {"type": "string", "format": "uint64", "title": "订单id"}}}, "grpcBenefitRecordResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/usergrpcBenefitRecord"}}, "page": {"$ref": "#/definitions/grpcPageResponse"}}}, "grpcChargeRequest": {"type": "object", "properties": {"points": {"type": "number", "format": "float", "title": "积分数"}, "reason": {"type": "string", "title": "充值原因"}}}, "grpcCheckMailCodeRequest": {"type": "object", "properties": {"email": {"type": "string"}, "emailVerifyCode": {"type": "string"}, "emailVerifyCodeToken": {"type": "string"}}}, "grpcCreateUserRequest": {"type": "object", "properties": {"email": {"type": "string", "title": "邮箱"}, "password": {"type": "string", "title": "密码，经过 sha1（passwd + salt（aibook）） 加密后"}, "country": {"type": "string", "title": "国家代码，3位"}, "language": {"type": "string", "title": "语言代码"}, "deviceInfo": {"$ref": "#/definitions/grpcDeviceInfo", "title": "设备信息"}, "emailVerifyCode": {"type": "string", "title": "邮箱验证码"}, "emailVerifyCodeToken": {"type": "string", "title": "验证码 token"}}}, "grpcDetailProfile": {"type": "object", "properties": {"userId": {"type": "string", "format": "uint64"}, "profile": {"$ref": "#/definitions/grpcUserProfile"}}}, "grpcDeviceInfo": {"type": "object", "properties": {"deviceId": {"type": "string"}, "deviceName": {"type": "string"}, "deviceType": {"type": "string"}, "osName": {"type": "string"}, "osVersion": {"type": "string"}, "appVersion": {"type": "string"}}}, "grpcEmpty": {"type": "object"}, "grpcGetEmailVerifyCodeResponse": {"type": "object", "properties": {"emailVerifyCodeToken": {"type": "string", "title": "验证码令牌"}, "emailVerifyCode": {"type": "string", "title": "验证码, 仅研发环境返回，生产环境不返回此值。"}}}, "grpcGetSystemFeaturePointsResponse": {"type": "object", "properties": {"records": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcSystemFeaturePoints"}}}}, "grpcLoginRequest": {"type": "object", "properties": {"email": {"type": "string", "title": "用户名"}, "password": {"type": "string", "title": "密码"}, "deviceInfo": {"$ref": "#/definitions/grpcDeviceInfo", "title": "设备信息"}}}, "grpcLoginResponse": {"type": "object", "properties": {"seq": {"type": "integer", "format": "int64", "title": "用户序列号"}, "token": {"type": "string", "title": "登录令牌, 2个月过期"}, "expireAt": {"type": "integer", "format": "int64", "title": "过期 unix 秒级时间戳"}, "userId": {"type": "string", "format": "uint64", "title": "用户ID, 仅研发联调时返回； 生产不返回"}, "ageRange": {"type": "string", "title": "年龄段: '2-3', '4-5', '6-7', '8+'"}}}, "grpcPageResponse": {"type": "object", "properties": {"page": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}}}, "grpcReduceBenefitRequest": {"type": "object", "properties": {"benefitType": {"type": "integer", "format": "int32", "title": "扣减权益类型（0-积分， 1-3 ：对应套餐里的功能权益，"}, "benefitReduceCount": {"type": "integer", "format": "int32", "description": "权益扣数量", "title": "1-图文绘本，2-动态绘本）"}, "pointReduceCount": {"type": "number", "format": "float", "title": "积分扣数量, 和权益类型对应"}, "reason": {"type": "string", "title": "扣减原因"}}}, "grpcResetPasswordRequest": {"type": "object", "properties": {"email": {"type": "string"}, "emailVerifyCode": {"type": "string"}, "emailVerifyCodeToken": {"type": "string"}, "newPassword": {"type": "string"}}}, "grpcSearchResponse": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcDetailProfile"}}, "page": {"type": "integer", "format": "int32", "title": "页码"}, "pageSize": {"type": "integer", "format": "int32", "title": "每页数量"}, "totalSize": {"type": "integer", "format": "int32", "title": "总数量"}}}, "grpcSystemFeaturePoints": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "记录ID"}, "country": {"type": "string", "title": "国家, 每个国家一条记录"}, "feature1Points": {"type": "number", "format": "float", "title": "套餐图文绘本权益用完后，创建一个图文绘本需要扣除的积分数"}, "feature2Points": {"type": "number", "format": "float", "title": "套餐动态绘本权益用完后，创建一个动态绘本需要扣除的积分数"}, "feature3Points": {"type": "number", "format": "float"}, "createdAt": {"type": "integer", "format": "int64", "title": "创建时间, 创建或修改记录时，此字段无效"}, "updatedAt": {"type": "integer", "format": "int64", "title": "更新时间, 创建或修改记录时，此字段无效"}}}, "grpcSystemPlan": {"type": "object", "properties": {"planId": {"type": "integer", "format": "int64", "title": "套餐ID"}, "name": {"type": "string", "title": "套餐名称"}, "description": {"type": "string", "title": "套餐描述"}, "price": {"type": "number", "format": "float", "title": "套餐价格"}, "planType": {"type": "integer", "format": "int32", "title": "套餐类型（0-新用户套餐， 1-普通套餐）"}, "country": {"type": "string", "title": "国家"}, "currency": {"type": "string", "title": "货币代码"}, "feature1Total": {"type": "integer", "format": "int32", "title": "权益1 - 图文绘本创建次数"}, "feature2Total": {"type": "integer", "format": "int32", "title": "权益2 - 动态绘本创建次数"}, "feature3Total": {"type": "integer", "format": "int32"}, "createdAt": {"type": "integer", "format": "int64", "title": "创建时间, 创建或修改记录时，此字段无效"}, "updatedAt": {"type": "integer", "format": "int64", "title": "更新时间, 创建或修改记录时，此字段无效"}}}, "grpcSystemPlans": {"type": "object", "properties": {"plans": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/grpcSystemPlan"}}}, "title": "根据国家过滤套餐，如果国家信息为空，则返回全部套餐信息"}, "grpcUint32Id": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}}, "grpcUpdateProfileRequest": {"type": "object", "properties": {"nickname": {"type": "string", "title": "更新用户信息请求，字段为空或0， 则代表不更改。"}, "lang": {"type": "string", "title": "语言代码"}, "birthday": {"type": "integer", "format": "int64", "title": "生日， unix 秒级时间戳"}}}, "grpcUserPlan": {"type": "object", "properties": {"planId": {"type": "integer", "format": "int32", "title": "套餐id"}, "planType": {"type": "integer", "format": "int32", "title": "套餐类型"}, "activatedAt": {"type": "integer", "format": "int64", "title": "秒级时间戳"}, "expiredAt": {"type": "integer", "format": "int64", "title": "unix 秒级时间戳"}, "feature1Remain": {"type": "integer", "format": "int32", "title": "权益1 - 图文绘本创建次数"}, "feature2Remain": {"type": "integer", "format": "int32", "title": "权益2 - 动态绘本创建次数"}, "feature3Remain": {"type": "integer", "format": "int32"}, "feature1Total": {"type": "integer", "format": "int32"}, "feature2Total": {"type": "integer", "format": "int32"}, "feature3Total": {"type": "integer", "format": "int32"}}}, "grpcUserProfile": {"type": "object", "properties": {"nickname": {"type": "string", "title": "昵称"}, "email": {"type": "string", "title": "邮箱"}, "status": {"type": "integer", "format": "int32", "title": "状态"}, "country": {"type": "string", "title": "国家或地区, 不可修改"}, "lang": {"type": "string", "title": "语言"}, "birthday": {"type": "integer", "format": "int64", "title": "生日"}, "points": {"type": "number", "format": "float", "title": "用户积分余额"}, "deviceId": {"type": "string", "title": "设备ID"}, "lastLoginAt": {"type": "integer", "format": "int64", "title": "最后登录时间"}, "createdAt": {"type": "integer", "format": "int64", "title": "创建时间"}, "updatedAt": {"type": "integer", "format": "int64", "title": "更新时间"}}}, "grpcVerifyEmailCodeResponse": {"type": "object", "properties": {"seq": {"type": "integer", "format": "int64", "title": "用户序列号, 0-用户不存在， 非0-用户存在"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "usergrpcBenefitRecord": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "benefitType": {"type": "integer", "format": "int64", "title": "'权益类型（1-3），0表示扣除积分, 1-3 表示扣除套餐中功能权益数量， 8-表示积分充值',"}, "benefitCount": {"type": "integer", "format": "int64", "title": "'扣减的权益次数',"}, "pointsUsed": {"type": "number", "format": "float", "title": "'本次消耗的积分',"}, "pointsRecharged": {"type": "number", "format": "float", "title": "'返还或充值的积分',"}, "reason": {"type": "string", "title": "'使用原因',"}, "createdAt": {"type": "integer", "format": "int64", "title": "'创建时间',"}, "updatedAt": {"type": "integer", "format": "int64", "title": "'更新时间',"}}}}}