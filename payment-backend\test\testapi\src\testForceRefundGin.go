package main

import (
	"fmt"
	"io"
	"time"
)

// testForceRefundGin 测试内网强制退款接口 (Gin HTTP)
func testForceRefundGin(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Force Refund Order (Internal Gin HTTP)"

	res, order_id_to_refund, err := GetCtxValue("order_id_to_refund", results, testName, start)
	if err != nil {
		return *res
	}

	orderID := order_id_to_refund
	url := config.LocalGinURL + "/api/v1/pay-service/admin/order-service/orders/" + orderID + "/force-refund"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	// 构造退款请求体 - 全额退款
	requestBody := RefundOrderRequest{
		Amount: nil, // nil表示全额退款
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Order force refunded successfully via Gin HTTP",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
