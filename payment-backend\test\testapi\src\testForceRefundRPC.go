package main

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

// testForceRefundRPC 测试内网强制退款接口 (Dubbo RPC)
func testForceRefundRPC(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Force Refund Order (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.payment.grpc.OrderService/ForceRefund"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 需要先有一个存在的 order_id_to_refund
	res, order_id_to_refund, err := GetCtxValue("order_id_to_refund", results, testName, start)
	if err != nil {
		return *res
	}

	// 使用一个示例订单ID，实际测试时需要替换为真实的订单ID
	orderID := order_id_to_refund

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := RefundOrderRequest{
		OrderID: orderID,
		Amount:  nil, // nil表示全额退款
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {

		var rsp RefundOrderResponse
		if err := json.Unmarshal(body, &rsp); err != nil {
			if detailLogger != nil {
				detailLogger.Printf("Failed to unmarshal response: %v", err)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  fmt.Sprintf("Failed to unmarshal response: %v", err),
				Duration: duration,
			}
		}
		detailLogger.Printf("RefundOrderResponse: %+v", rsp)
		if !rsp.Success {
			if detailLogger != nil {
				detailLogger.Printf("Request failed: %s", rsp.Message)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  rsp.Message,
				Duration: duration,
			}
		}

		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Order force refunded successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
