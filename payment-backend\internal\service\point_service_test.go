package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"payment-backend/internal/config"
	"payment-backend/internal/logger"
)

func TestNewPointService(t *testing.T) {
	cfg := &config.Config{
		Nacos: config.NacosConfig{
			Enabled:   false, // 测试环境下禁用 Nacos
			Endpoints: []string{"127.0.0.1:8848"},
			Namespace: "test",
			Username:  "test",
			Password:  "test",
		},
	}

	log, err := logger.NewLogger(&config.LogConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	service := NewPointService(cfg, log)
	assert.NotNil(t, service)
}

func TestPointService_ChargePoints_ValidationErrors(t *testing.T) {
	cfg := &config.Config{
		Nacos: config.NacosConfig{
			Enabled:   false, // 测试环境下禁用 Nacos
			Endpoints: []string{"127.0.0.1:8848"},
			Namespace: "test",
			Username:  "test",
			Password:  "test",
		},
	}

	log, err := logger.NewLogger(&config.LogConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	service := NewPointService(cfg, log)
	ctx := context.Background()

	// 测试无效的积分数值
	err = service.ChargePoints(ctx, 0, "test reason")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "points must be greater than 0")

	err = service.ChargePoints(ctx, -10, "test reason")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "points must be greater than 0")

	err = service.ChargePoints(ctx, 1000001, "test reason")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "points must be less than or equal to 1000000")

	// 测试无效的原因
	err = service.ChargePoints(ctx, 100, "")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "reason length must be between 1 and 88 characters")

	longReason := make([]byte, 89)
	for i := range longReason {
		longReason[i] = 'a'
	}
	err = service.ChargePoints(ctx, 100, string(longReason))
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "reason length must be between 1 and 88 characters")
}

func TestPointService_ChargePoints_NacosDisabled(t *testing.T) {
	cfg := &config.Config{
		Nacos: config.NacosConfig{
			Enabled: false, // 禁用 Nacos
		},
	}

	log, err := logger.NewLogger(&config.LogConfig{
		Level:  "info",
		Format: "json",
		Output: "stdout",
	})
	require.NoError(t, err)

	service := NewPointService(cfg, log)
	ctx := context.Background()

	// 当 Nacos 禁用时，应该返回错误
	err = service.ChargePoints(ctx, 100, "test reason")
	require.Error(t, err)
	assert.Contains(t, err.Error(), "nacos is not enabled")
}
