package main

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

// ChargePointsHTTPRequest HTTP充值积分请求
type ChargePointsHTTPRequest struct {
	Points float32 `json:"points"` // 积分数
	Reason string  `json:"reason"` // 充值原因
}

// testChargePointsHTTP 测试HTTP方式调用用户服务充值积分接口
func testChargePointsHTTP(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Charge Points (HTTP)"

	// 构建用户服务的HTTP接口地址
	// 根据protobuf定义，HTTP路径是 /api/v1/user/charge
	url := "http://localhost:8081/api/v1/user/charge" // 假设用户服务运行在8081端口
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "test_user_123",
		"x-country":  "US",
	}

	// 构造请求体
	requestBody := ChargePointsHTTPRequest{
		Points: 100.5,
		Reason: "测试HTTP方式充值积分",
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)

	if resp.StatusCode == 200 {
		// 根据用户服务的实际响应格式解析
		// 这里假设返回的是空响应或者简单的成功响应
		
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Points charged successfully via HTTP",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testChargePointsHTTPWithService 测试使用我们的PointServiceHTTP服务
func testChargePointsHTTPWithService(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Charge Points (HTTP Service)"

	// 这个测试需要在实际的应用环境中运行，因为需要依赖注入
	// 这里只是一个示例，展示如何使用PointServiceHTTP
	
	/*
	// 在实际应用中的使用方式：
	pointServiceHTTP := service.NewPointServiceHTTP(cfg, logger)
	
	ctx := context.Background()
	ctx = pointServiceHTTP.WithUserContext(ctx, "test_user_123", "US", "test_trace_id")
	
	err := pointServiceHTTP.ChargePoints(ctx, 100.5, "测试HTTP方式充值积分")
	if err != nil {
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Service call failed: %v", err),
			Duration: time.Since(start),
		}
	}
	*/

	duration := time.Since(start)
	
	return TestResult{
		TestName: testName,
		Success:  true,
		Message:  "HTTP service test placeholder - requires application context",
		Duration: duration,
	}
}
