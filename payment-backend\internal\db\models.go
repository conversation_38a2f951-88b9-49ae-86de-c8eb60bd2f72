package db

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"crypto/rand"
	"math/big"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bwmarrin/snowflake"
	"gorm.io/gorm"

	dbStore "payment-backend/internal/db/store"
	"payment-backend/internal/domain"
)

// JSONMap 用于存储JSON格式的map[string]any
type JSONMap map[string]any

// Value 实现driver.Valuer接口
func (j JSONMap) Value() (driver.Value, error) {
	if j == nil {
		return nil, nil
	}
	return json.Marshal(j)
}

// Scan 实现sql.Scanner接口
func (j *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*j = nil
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into JSONMap", value)
	}

	return json.Unmarshal(bytes, j)
}

// 雪花算法节点（全局变量）
var (
	snowflakeNode *snowflake.Node
	snowflakeOnce sync.Once
)

// InitSnowflake 初始化雪花算法节点
func InitSnowflake(nodeID int64) error {
	var err error
	snowflakeOnce.Do(func() {
		snowflakeNode, err = snowflake.NewNode(nodeID)
	})
	return err
}

// GenerateOrderID 生成订单ID
// 格式: 订单创建时间 + 4位安全随机数 + PSP 厂商 + 雪花算法ID
func GenerateOrderID(PSPProvider string) string {
	now := time.Now()
	timeStr := now.Format("20060102150405") // YYYYMMDDHHMMSS

	// 如果雪花算法节点未初始化，使用默认节点0
	if snowflakeNode == nil {
		InitSnowflake(0)
	}

	n, err := rand.Int(rand.Reader, big.NewInt(10000))
	if err != nil {
		n = big.NewInt(0)
	}

	snowflakeID := snowflakeNode.Generate().Int64()
	return fmt.Sprintf("%s%04d%s%d", timeStr, n.Int64(), strings.ToUpper(PSPProvider), snowflakeID)
}

// OrderModel 订单数据库模型
type OrderModel struct {
	ID                  uint64     `gorm:"primaryKey;autoIncrement" json:"id"`
	OrderID             string     `gorm:"type:varchar(64);not null;uniqueIndex:idx_orders_order_id" json:"order_id"`
	UserID              string     `gorm:"type:varchar(64);not null;index:idx_orders_user_id" json:"user_id"`
	ProductID           string     `gorm:"type:varchar(64);not null;index:idx_orders_product_id" json:"product_id"`
	ProductDesc         string     `gorm:"type:text" json:"product_desc"`
	ProductSnapshot     string     `gorm:"type:text" json:"product_napshot"`
	PriceID             string     `gorm:"type:varchar(64);not null;index:idx_orders_price_id" json:"price_id"`
	Quantity            uint32     `gorm:"not null;default:1" json:"quantity"`
	Amount              string     `gorm:"type:decimal(18,2);not null" json:"amount"`
	NetAmount           string     `gorm:"type:decimal(18,2)" json:"net_amount"`
	Currency            string     `gorm:"type:char(3);index:idx_currency" json:"currency"`
	PayStatus           string     `gorm:"type:varchar(20);not null;index:idx_orders_pay_status;default:'created'" json:"pay_status"`
	PayRet              string     `gorm:"type:varchar(512)" json:"pay_ret"`
	PayedMethod         string     `gorm:"type:varchar(32)" json:"payed_method"`
	PSPProvider         string     `gorm:"type:varchar(32);not null;index:idx_psp_provider" json:"psp_provider"`
	CardNumber          string     `gorm:"type:varchar(32)" json:"card_number"`
	PayedAt             *time.Time `gorm:"index:idx_payed_at" json:"payed_at"`
	RefundStatus        string     `gorm:"type:varchar(20);not null;index:idx_orders_refund_status;default:'none'" json:"refund_status"`
	RefundedAt          *time.Time `gorm:"index:idx_refunded_at" json:"refunded_at"`
	PSPPaymentID        string     `gorm:"type:varchar(128);index:idx_orders_psp_payment_id" json:"psp_payment_id"`
	PSPPaymentIntentID  string     `gorm:"type:varchar(128);index:idx_orders_psp_payment_intent_id" json:"psp_payment_intent_id"`
	PSPPaymentRefundID  string     `gorm:"type:varchar(128);index:idx_orders_psp_payment_refund_id" json:"psp_payment_refund_id"`
	PSPPaymentRefundRet string     `gorm:"type:varchar(512)" json:"psp_payment_refund_ret"`
	PSPCustomerID       string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_id" json:"psp_customer_id"`
	PSPCustomerEmail    string     `gorm:"type:varchar(128);index:idx_orders_psp_customer_email" json:"psp_customer_email"`
	PSPSubscriptionID   string     `gorm:"type:varchar(128)" json:"psp_subscription_id"`
	CreatedAt           *time.Time `gorm:"not null;index:idx_orders_created_at" json:"created_at"`
	UpdatedAt           *time.Time `gorm:"not null" json:"updated_at"`
	Deleted             uint8      `gorm:"not null;default:0;index:idx_orders_deleted" json:"deleted"`
	DeletedAt           *time.Time `json:"deleted_at"`
}

// TableName 指定表名
func (OrderModel) TableName() string {
	return "payment_orders"
}

// BeforeCreate GORM钩子，在创建前执行
func (o *OrderModel) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	o.CreatedAt = &now
	o.UpdatedAt = &now

	// 如果没有设置OrderID，则自动生成
	if o.OrderID == "" {
		o.OrderID = GenerateOrderID(o.PSPProvider)
	}

	return nil
}

// BeforeUpdate GORM钩子，在更新前执行
func (o *OrderModel) BeforeUpdate(tx *gorm.DB) error {
	now := time.Now()
	o.UpdatedAt = &now
	return nil
}

// ToDomain 转换为domain模型
func (o *OrderModel) ToDomain() *domain.Order {
	amount, _ := strconv.ParseFloat(o.Amount, 64)
	netAmount, _ := strconv.ParseFloat(o.NetAmount, 64)

	return &domain.Order{
		ID:                  o.ID,
		OrderID:             o.OrderID,
		UserID:              o.UserID,
		ProductID:           o.ProductID,
		ProductDesc:         o.ProductDesc,
		ProductSnapshot:     o.ProductSnapshot,
		PriceID:             o.PriceID,
		Quantity:            o.Quantity,
		Amount:              amount,
		NetAmount:           netAmount,
		Currency:            o.Currency,
		PayStatus:           o.PayStatus,
		PayRet:              o.PayRet,
		PayedMethod:         o.PayedMethod,
		PSPProvider:         o.PSPProvider,
		CardNumber:          o.CardNumber,
		PayedAt:             o.PayedAt,
		RefundStatus:        o.RefundStatus,
		RefundedAt:          o.RefundedAt,
		PSPPaymentID:        o.PSPPaymentID,
		PSPPaymentIntentID:  o.PSPPaymentIntentID,
		PSPPaymentRefundID:  o.PSPPaymentRefundID,
		PSPPaymentRefundRet: o.PSPPaymentRefundRet,
		PSPCustomerID:       o.PSPCustomerID,
		PSPCustomerEmail:    o.PSPCustomerEmail,
		PSPSubscriptionID:   o.PSPSubscriptionID,
		CreatedAt:           o.CreatedAt,
		UpdatedAt:           o.UpdatedAt,
		Deleted:             o.Deleted > 0,
		DeletedAt:           o.DeletedAt,
	}
}

// FromDomain 从domain模型创建
func (o *OrderModel) FromDomain(order *domain.Order) {
	o.ID = order.ID
	o.OrderID = order.OrderID
	o.UserID = order.UserID
	o.ProductID = order.ProductID
	o.ProductDesc = order.ProductDesc
	o.ProductSnapshot = order.ProductSnapshot
	o.PriceID = order.PriceID
	o.Quantity = order.Quantity
	o.Amount = fmt.Sprintf("%.2f", order.Amount)
	o.NetAmount = fmt.Sprintf("%.2f", order.NetAmount)
	o.Currency = order.Currency
	o.PayStatus = order.PayStatus
	o.PayRet = order.PayRet
	o.PayedMethod = order.PayedMethod
	o.PSPProvider = order.PSPProvider
	o.CardNumber = order.CardNumber
	o.PayedAt = order.PayedAt
	o.RefundStatus = order.RefundStatus
	o.RefundedAt = order.RefundedAt
	o.PSPPaymentID = order.PSPPaymentID
	o.PSPPaymentIntentID = order.PSPPaymentIntentID
	o.PSPPaymentRefundID = order.PSPPaymentRefundID
	o.PSPPaymentRefundRet = order.PSPPaymentRefundRet
	o.PSPCustomerID = order.PSPCustomerID
	o.PSPCustomerEmail = order.PSPCustomerEmail
	o.PSPSubscriptionID = order.PSPSubscriptionID
	o.CreatedAt = order.CreatedAt
	o.UpdatedAt = order.UpdatedAt
	if order.Deleted {
		o.Deleted = 1
	} else {
		o.Deleted = 0
	}
	o.DeletedAt = order.DeletedAt
}

// AutoMigrate 自动迁移数据库表结构
func AutoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&OrderModel{},
		&dbStore.PackageModel{},
	)
}
