{"openapi": "3.1.0", "info": {"title": "AI Companion Picture Book Service", "version": "0.1.0"}, "paths": {"/picture_book/generate_story": {"post": {"tags": ["绘本创作"], "summary": "Generate Story Endpoint", "description": "生成绘本故事（支持流式和非流式）", "operationId": "generate_story_endpoint_picture_book_generate_story_post", "parameters": [{"name": "stream", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Stream"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateStoryRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateStoryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/picture_book/generate_content": {"post": {"tags": ["绘本创作"], "summary": "Generate Content", "description": "生成绘本内容", "operationId": "generate_content_picture_book_generate_content_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateContentRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateContentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/picture_book/confirm": {"post": {"tags": ["绘本创作"], "summary": "Confirm", "description": "确认绘本", "operationId": "confirm_picture_book_confirm_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/picture_book/list": {"get": {"tags": ["绘本创作"], "summary": "Picture Books", "description": "列出绘本", "operationId": "picture_books_picture_book_list_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PictureBookListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/picture_book/{book_id}": {"get": {"tags": ["绘本创作"], "summary": "Picture Book Details", "description": "获取绘本详情", "operationId": "picture_book_details_picture_book__book_id__get", "parameters": [{"name": "book_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Book Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PictureBookDetail"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/character/admin/add_base_character": {"post": {"tags": ["角色管理"], "summary": "Add Base Character", "description": "管理员添加基础角色", "operationId": "add_base_character_character_admin_add_base_character_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseCharacterCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseCharacterResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/character/base_characters": {"get": {"tags": ["角色管理"], "summary": "Get Base Characters", "description": "列出基础角色", "operationId": "get_base_characters_character_base_characters_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BaseCharacterListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/character/admin/character_images": {"get": {"tags": ["角色管理"], "summary": "Get Character Images", "description": "列出基础角色", "operationId": "get_character_images_character_admin_character_images_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "size", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Size"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterImageListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/character/character_images": {"get": {"tags": ["角色管理"], "summary": "Get Character Image Endpoint", "description": "查询具体形象", "operationId": "get_character_image_endpoint_character_character_images_get", "parameters": [{"name": "base_character_id", "in": "query", "required": true, "schema": {"type": "string", "title": "Base Character Id"}}, {"name": "color", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CharacterColor"}}, {"name": "personality", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CharacterPersonality"}}, {"name": "gender", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/CharacterGender"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CharacterImageResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/character/user_partner": {"post": {"tags": ["角色管理"], "summary": "Create User Partner Endpoint", "description": "创建用户专属陪伴角色", "operationId": "create_user_partner_endpoint_character_user_partner_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartnerCreate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserPartnerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/chat/": {"post": {"tags": ["AI 对话"], "summary": "Cha<PERSON>", "description": "AI 对话接口，支持流式响应", "operationId": "chat_chat__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"BaseCharacterCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "base_image": {"type": "string", "title": "Base Image"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "required": ["name", "base_image"], "title": "BaseCharacterCreate"}, "BaseCharacterListResponse": {"properties": {"characters": {"items": {"$ref": "#/components/schemas/BaseCharacterResponse"}, "type": "array", "title": "Characters"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["characters", "total"], "title": "BaseCharacterListResponse"}, "BaseCharacterResponse": {"properties": {"base_character_id": {"type": "string", "title": "Base Character Id"}, "name": {"type": "string", "title": "Name"}, "base_image": {"type": "string", "title": "Base Image"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "create_time": {"type": "string", "format": "date-time", "title": "Create Time"}}, "type": "object", "required": ["base_character_id", "name", "base_image", "description", "create_time"], "title": "BaseCharacterResponse"}, "Character": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "角色名称"}, "features": {"type": "string", "title": "Features", "description": "角色特征"}}, "type": "object", "required": ["features"], "title": "Character"}, "CharacterColor": {"type": "string", "enum": ["brown", "yellow"], "title": "CharacterColor"}, "CharacterGender": {"type": "string", "enum": ["male", "female"], "title": "CharacterGender"}, "CharacterImageListResponse": {"properties": {"character_images": {"items": {"$ref": "#/components/schemas/CharacterImageResponse"}, "type": "array", "title": "Character Images"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["character_images", "total"], "title": "CharacterImageListResponse"}, "CharacterImageResponse": {"properties": {"character_id": {"type": "string", "title": "Character Id"}, "base_character_id": {"type": "string", "title": "Base Character Id"}, "color": {"$ref": "#/components/schemas/CharacterColor"}, "personality": {"$ref": "#/components/schemas/CharacterPersonality"}, "gender": {"$ref": "#/components/schemas/CharacterGender"}, "features": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Features"}, "happy_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Happy Image"}, "sad_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sad Image"}, "surprised_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Surprised Image"}, "calm_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Calm Image"}, "curious_image": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Curious Image"}, "create_time": {"type": "string", "format": "date-time", "title": "Create Time"}}, "type": "object", "required": ["character_id", "base_character_id", "color", "personality", "gender", "features", "happy_image", "sad_image", "surprised_image", "calm_image", "curious_image", "create_time"], "title": "CharacterImageResponse"}, "CharacterPersonality": {"type": "string", "enum": ["brave"], "title": "CharacterPersonality"}, "ChatRequest": {"properties": {"message": {"type": "string", "title": "Message"}, "partner_id": {"type": "string", "title": "Partner Id"}, "session_id": {"type": "string", "title": "Session Id"}}, "type": "object", "required": ["message", "partner_id", "session_id"], "title": "ChatRequest"}, "ChatResponse": {"properties": {"session_id": {"type": "string", "title": "Session Id"}, "message": {"type": "string", "title": "Message"}, "emotion": {"type": "string", "title": "Emotion"}, "function_call": {"type": "object", "title": "Function Call"}}, "type": "object", "required": ["session_id", "message", "emotion"], "title": "ChatResponse"}, "ConfirmRequest": {"properties": {"book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}}, "type": "object", "required": ["book_id"], "title": "ConfirmRequest"}, "ConfirmResponse": {"properties": {"book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}, "status": {"$ref": "#/components/schemas/PictureBookStatus", "description": "绘本状态"}}, "type": "object", "required": ["book_id", "status"], "title": "ConfirmResponse"}, "GenerateContentRequest": {"properties": {"session_id": {"type": "string", "title": "Session Id", "description": "会话ID"}, "story": {"type": "string", "title": "Story", "description": "用户故事"}, "user_character_id": {"type": "integer", "title": "User Character Id", "description": "用户角色 ID"}, "idempotency_key": {"type": "string", "title": "Idempotency Key", "description": "幂等键"}}, "type": "object", "required": ["session_id", "story", "user_character_id"], "title": "GenerateContentRequest"}, "GenerateContentResponse": {"properties": {"book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}}, "type": "object", "required": ["book_id"], "title": "GenerateContentResponse"}, "GenerateStoryRequest": {"properties": {"session_id": {"type": "string", "title": "Session Id", "description": "会话ID"}, "parent_input": {"type": "string", "title": "Parent Input", "description": "家长输入的故事描述"}, "user_character_id": {"type": "integer", "title": "User Character Id", "description": "用户角色 ID"}}, "type": "object", "required": ["session_id", "parent_input", "user_character_id"], "title": "GenerateStoryRequest"}, "GenerateStoryResponse": {"properties": {"session_id": {"type": "string", "title": "Session Id", "description": "会话 ID"}, "story": {"type": "string", "title": "Story", "description": "用户故事"}}, "type": "object", "required": ["session_id", "story"], "title": "GenerateStoryResponse"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "PictureBookDetail": {"properties": {"book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "绘本标题"}, "status": {"$ref": "#/components/schemas/PictureBookStatus", "description": "绘本状态"}, "create_time": {"type": "string", "format": "date-time", "title": "Create Time", "description": "绘本创建时间"}, "update_time": {"type": "string", "format": "date-time", "title": "Update Time", "description": "绘本更新时间"}, "characters": {"items": {"$ref": "#/components/schemas/Character"}, "type": "array", "title": "Characters", "description": "角色列表"}, "themes": {"items": {"type": "string"}, "type": "array", "title": "Themes", "description": "绘本主题"}, "questions": {"items": {"additionalProperties": {"type": "string"}, "type": "object"}, "type": "array", "title": "Questions", "description": "绘本互动问题"}, "story": {"type": "string", "title": "Story", "description": "绘本内容"}, "background_music": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Background Music", "description": "绘本背景音乐"}, "scenes": {"anyOf": [{"items": {"$ref": "#/components/schemas/SceneDetail"}, "type": "array"}, {"type": "null"}], "title": "Scenes", "description": "绘本场景"}, "idempotency_key": {"type": "string", "title": "Idempotency Key", "description": "幂等键"}}, "type": "object", "required": ["book_id", "status", "create_time", "update_time", "characters", "themes", "questions", "story", "idempotency_key"], "title": "PictureBookDetail"}, "PictureBookListItem": {"properties": {"book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "绘本标题"}, "story": {"type": "string", "title": "Story", "description": "绘本内容"}, "themes": {"items": {"type": "string"}, "type": "array", "title": "Themes", "description": "绘本主题"}, "status": {"$ref": "#/components/schemas/PictureBookStatus", "description": "绘本状态"}, "create_time": {"type": "string", "format": "date-time", "title": "Create Time", "description": "绘本创建时间"}, "update_time": {"type": "string", "format": "date-time", "title": "Update Time", "description": "绘本更新时间"}}, "type": "object", "required": ["book_id", "story", "themes", "status", "create_time", "update_time"], "title": "PictureBookListItem"}, "PictureBookListResponse": {"properties": {"books": {"items": {"$ref": "#/components/schemas/PictureBookListItem"}, "type": "array", "title": "Books", "description": "绘本列表"}, "total": {"type": "integer", "title": "Total", "description": "绘本总数"}}, "type": "object", "required": ["books", "total"], "title": "PictureBookListResponse"}, "PictureBookStatus": {"type": "string", "enum": ["draft", "processing", "generated", "completed", "discarded", "failed", "shared"], "title": "PictureBookStatus"}, "SceneDetail": {"properties": {"scene_id": {"type": "string", "title": "Scene Id", "description": "场景ID"}, "book_id": {"type": "string", "title": "Book Id", "description": "绘本ID"}, "page_number": {"type": "integer", "title": "Page Number", "description": "场景页码"}, "description": {"type": "string", "title": "Description", "description": "场景描述"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Content", "description": "内容文本"}, "illustration": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Illustration", "description": "场景图片"}, "video": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Video", "description": "场景视频"}, "sound_effect": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sound Effect", "description": "场景音效"}, "characters": {"anyOf": [{"items": {"$ref": "#/components/schemas/Character"}, "type": "array"}, {"type": "null"}], "title": "Characters", "description": "场景角色"}}, "type": "object", "required": ["scene_id", "book_id", "page_number", "description"], "title": "SceneDetail"}, "UserPartnerCreate": {"properties": {"character_id": {"type": "string", "title": "Character Id"}, "name": {"type": "string", "title": "Name"}, "age": {"type": "integer", "title": "Age"}}, "type": "object", "required": ["character_id", "name", "age"], "title": "UserPartnerCreate"}, "UserPartnerResponse": {"properties": {"partner_id": {"type": "string", "title": "Partner Id"}, "character_id": {"type": "string", "title": "Character Id"}, "name": {"type": "string", "title": "Name"}, "age": {"type": "integer", "title": "Age"}, "create_time": {"type": "string", "format": "date-time", "title": "Create Time"}}, "type": "object", "required": ["partner_id", "character_id", "name", "age", "create_time"], "title": "UserPartnerResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "admin_token"}}}}}}