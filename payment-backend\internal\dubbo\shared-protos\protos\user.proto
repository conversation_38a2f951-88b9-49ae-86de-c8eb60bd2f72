syntax = "proto3";

package com.aibook.user.grpc;
option java_multiple_files = true;
option go_package = "./userpb;userpb";

import "google/api/annotations.proto";
import "validate.proto";

service UserService {

  // 获取邮箱验证码
  rpc GetEmailVerifyCode(GetEmailVerifyCodeRequest) returns (GetEmailVerifyCodeResponse) {
    option (google.api.http) = {
      get : "/api/v1/user/email_verify_code"
    };
  }

  // 校验邮箱验证码
  rpc VerifyEmailCode(CheckMailCodeRequest) returns (VerifyEmailCodeResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/verify_email_code"
      body : "*"
    };
  }

  // 创建用户
  rpc CreateUser(CreateUserRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/create"
      body : "*"
    };
  }

  // 通过邮箱和密码登录
  rpc Login(LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/login"
      body : "*"
    };
  }

  // 通过邮箱和验证码登录
  rpc LoginWithCode(CheckMailCodeRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/login_with_code"
      body : "*"
    };
  }

  // 刷新 token （需认证 token）
  rpc RefreshToken(Empty) returns (LoginResponse) {
    option (google.api.http) = {
      post : "/api/v1/user/refresh_token"
      body : "*"
    };
  }

  // 获取用户信息（需认证 token）
  rpc GetProfile(GetProfileRequest) returns (UserProfile) {
    option (google.api.http) = {
      get : "/api/v1/user/profile"
    };
  }

  // 更新用户信息（需认证 token）
  rpc UpdateProfile(UpdateProfileRequest) returns (UserProfile) {
    option (google.api.http) = {
      post : "/api/v1/user/profile"
      body : "*"
    };
  }

  // 找回密码
  rpc ResetPassword(ResetPasswordRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/user/reset_password"
      body : "*"
    };
  }




  // 创建或修改系统套餐(套餐id为0表示创建) （非终端访问接口）
  rpc UpdateSystemPlan(SystemPlan) returns (Empty) {
    option (google.api.http) = {
      put : "/api/v1/user/sys/plan"
      body : "*"
    };
  }
 
  // 用户请求，则根据国家代码筛选；管理平台请求，返回所有国家套餐。 (管理平台调用)
  rpc GetSystemPlans(Empty) returns (SystemPlans) {
    option (google.api.http) = {
      get : "/api/v1/user/sys/plans"
    };
  }



  // 删除系统套餐（非终端访问接口）
  rpc DeleteSystemPlan(DeleteSystemPlanRequest) returns (Empty) {
    option (google.api.http) = {
      delete : "/api/v1/user/sys/plan"
    };
  }

  // 创建或修改功能积分价格记录（id为0表示创建）（非终端访问接口）
  rpc UpdateSystemFeaturePoints(SystemFeaturePoints) returns (Uint32Id) {
    option (google.api.http) = {
      put : "/api/v1/user/sys/feature_points"
      body : "*"
    };
  }

  // 查询功能积分价格记录，返回所有记录。（非终端访问接口）
  rpc GetSystemFeaturePoints(Empty) returns (GetSystemFeaturePointsResponse) {
    option (google.api.http) = {
      get : "/api/v1/user/sys/feature_points"
    };
  }

  // 删除功能积分价格记录（非终端访问接口）
  rpc DeleteSystemFeaturePoints(DeleteSystemFeaturePointsRequest) returns (Empty) {
    option (google.api.http) = {
      delete : "/api/v1/user/sys/feature_points"
    };
  }

  // 获取用户套餐信息（需认证 token）
  rpc GetPlan(Empty) returns (UserPlan) {
    option (google.api.http) = {
      get : "/api/v1/user/plan"
    };
  }

  // 权益扣减（需认证 token）
  rpc ReducePlan(ReduceBenefitRequest) returns (BenefitOrder) {
    option (google.api.http) = {
      post : "/api/v1/user/plan/reduce"
      body : "*"
    };
  }

  // 回退一半积分权益 - ai 模块调用
  rpc RollbackHalfPointBenefit(BenefitOrder) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/user/plan/rollback"
      body : "*"
    };
  }

  // 权益使用记录
  rpc BenefitRecord(PageRequest) returns (BenefitRecordResponse) {
    option (google.api.http) = {
      get : "/api/v1/user/plan/record"
    };
  }

  // 积分充值
  rpc ChargePoints(ChargeRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/user/charge"
      body : "*"
    };
  }

  // 注销用户（需认证 token）
  rpc Delete(Empty) returns (Empty) {
    option (google.api.http) = {
      delete : "/api/v1/user"
    };
  }

  // 查询用户， 条件为空时，则列举全部用户；不为空时，多个条件为且的关系给结果
  rpc Search(SearchRequest) returns (SearchResponse) {
    option (google.api.http) = {
      get : "/api/v1/user/search"
    };
  }
}

message ChargeRequest {
  float points = 1 [(validate.rules).float = {gte: 1.0, lte: 1000000.0}];  // 积分数
  string reason = 2 [(validate.rules).string = {min_len: 1,max_len: 88}]; // 充值原因
}

message PageRequest {
  int32 page = 1 [(validate.rules).int32 = {gte: 1, lte: 100000}];              // 页码(从 1 开始)
  int32 page_size = 2 [(validate.rules).int32 = {gte: 1, lte: 10000}];         // 每页数量
}

message PageResponse {
  int32 page = 1;
  int32 page_size = 2;
  int32 total = 3;
}

message BenefitRecordResponse {
  repeated BenefitRecord records = 1;
  PageResponse page = 2;
}

message BenefitRecord {
  uint64 id = 1;
  uint32 benefit_type = 2;    // '权益类型（1-3），0表示扣除积分, 1-3 表示扣除套餐中功能权益数量， 8-表示积分充值',
  uint32 benefit_count = 3;   // '扣减的权益次数',
  float points_used = 4;      // '本次消耗的积分',
  float points_recharged = 5; // '返还或充值的积分',
  string reason = 6;          // '使用原因',
  uint32 created_at = 7;      // '创建时间',
  uint32 updated_at = 8;      // '更新时间',
}

// 搜索用户
message SearchRequest {
  int32 page = 1 [(validate.rules).int32 = {gte: 1, lte: 100000}];              // 页码(从 1 开始)
  int32 page_size = 2 [(validate.rules).int32 = {gte: 1, lte: 10000}];         // 每页数量
  string searchByNickname = 3; // 根据昵称查询
  string searchByEmail = 4;    // 根据邮箱查询
  string searchByDeviceId = 5; // 根据设备序列号查询
  uint64 searchByUserId = 6;   // 根据用户ID查询
}

message SearchResponse {
  repeated DetailProfile users = 1;
  int32 page = 2;       // 页码
  int32 page_size = 3;  // 每页数量
  int32 total_size = 4; // 总数量
}

message Empty {}

message DeleteSystemPlanRequest {
  uint32 plan_id = 1 [(validate.rules).uint32 = {gte: 1}]; // 套餐id
}

message DeleteSystemFeaturePointsRequest {
  uint32 id = 1 [(validate.rules).uint32 = {gte: 1}]; // 记录id
}

// 根据国家过滤套餐，如果国家信息为空，则返回全部套餐信息
message SystemPlans {
  repeated SystemPlan plans = 1; //
}

message SystemFeaturePoints {
  uint32 id = 1;             // 记录ID
  string country = 2 [(validate.rules).string = {min_len: 2,max_len: 3,pattern: "^[A-Z]+$"}];        // 国家, 每个国家一条记录
  float feature1_points = 3 [(validate.rules).float = {gte: 0.1}]; //套餐图文绘本权益用完后，创建一个图文绘本需要扣除的积分数
  float feature2_points = 4 [(validate.rules).float = {gte: 0.1}]; //套餐动态绘本权益用完后，创建一个动态绘本需要扣除的积分数
  float feature3_points = 5 [(validate.rules).float = {gte: 0.0}];
  uint32 created_at = 6; // 创建时间, 创建或修改记录时，此字段无效
  uint32 updated_at = 7; // 更新时间, 创建或修改记录时，此字段无效
}

message Uint32Id {
  uint32 id = 1;
}


message GetSystemFeaturePointsResponse {
  repeated SystemFeaturePoints records = 1;
}

message SystemPlan {
  uint32 plan_id = 1;     // 套餐ID
  string name = 2;        // 套餐名称
  string description = 3; // 套餐描述
  float price = 4;        // 套餐价格
  int32 plan_type = 5;    // 套餐类型（0-新用户套餐， 1-普通套餐）
  string country = 6 [(validate.rules).string = {min_len: 2,max_len: 3,pattern: "^[A-Z]+$"}];     // 国家
  string currency = 7;    // 货币代码
  int32 feature1_total = 8 [(validate.rules).int32 = {gte: 1}];     // 权益1 - 图文绘本创建次数
  int32 feature2_total = 9 [(validate.rules).int32 = {gte: 1}];     // 权益2 - 动态绘本创建次数
  int32 feature3_total = 10 [(validate.rules).int32 = {gte: 0}];
  uint32 created_at = 11; // 创建时间, 创建或修改记录时，此字段无效
  uint32 updated_at = 12; // 更新时间, 创建或修改记录时，此字段无效
  
}

message UserPlan {
  int32 plan_id = 1;         // 套餐id
  int32 plan_type = 2;       // 套餐类型
  uint32 activated_at = 3;   // 秒级时间戳
  uint32 expired_at = 4;     // unix 秒级时间戳
  int32 feature1_remain = 5; // 权益1 - 图文绘本创建次数
  int32 feature2_remain = 6; // 权益2 - 动态绘本创建次数
  int32 feature3_remain = 7;
  int32 feature1_total = 8; 
  int32 feature2_total = 9; 
  int32 feature3_total = 10;
}

message ReduceBenefitRequest {
  int32 benefit_type = 1 [(validate.rules).int32 = {gte: 0, lte: 3}]; // 扣减权益类型（0-积分， 1-3 ：对应套餐里的功能权益，
                          // 1-图文绘本，2-动态绘本）
  int32 benefit_reduce_count = 2 [(validate.rules).int32 = {gte: 0, lte: 10000}]; // 权益扣数量
  float point_reduce_count = 3 [(validate.rules).float = {gte: 0.0, lte: 10000.0}];   // 积分扣数量, 和权益类型对应
  string reason = 4 [(validate.rules).string = {min_len: 1,max_len: 88}];              // 扣减原因
}

message BenefitOrder {
  uint64 order_id = 1; // 订单id
}

message DeviceInfo {
  string device_id = 1;
  string device_name = 2;
  string device_type = 3;
  string os_name = 4;
  string os_version = 5;
  string app_version = 6;
}

message CreateUserRequest {
  string email = 1 [(validate.rules).string.email = true] ;    // 邮箱
  string password = 2 [(validate.rules).string = {min_len: 6,max_len: 188}]; // 密码，经过 sha1（passwd + salt（aibook）） 加密后
  string country = 3 [(validate.rules).string = {min_len: 2,max_len: 3,pattern: "^[A-Z]+$"}];  // 国家代码，3位
  string language = 4 [(validate.rules).string = {min_len: 2,max_len: 88}]; // 语言代码
  DeviceInfo device_info = 5;         // 设备信息
  string email_verify_code = 6 ;       //  邮箱验证码
  string email_verify_code_token = 7; // 验证码 token
}

message LoginRequest {
  string email = 1 [(validate.rules).string.email = true] ;    // 用户名
  string password = 2; // 密码
  DeviceInfo device_info = 3;         // 设备信息
}

message LoginResponse {
  uint32 seq = 1;       // 用户序列号
  string token = 2;     // 登录令牌, 2个月过期
  uint32 expire_at = 3; // 过期 unix 秒级时间戳
  uint64 user_id = 4;   // 用户ID, 仅研发联调时返回； 生产不返回
  string age_range = 5; // 年龄段: '2-3', '4-5', '6-7', '8+'
}

message  CheckMailCodeRequest {
  string email = 1 [(validate.rules).string.email = true] ;
  string email_verify_code = 2 ;
  string email_verify_code_token = 3 ;
}

message VerifyEmailCodeResponse {
   uint32 seq = 1;       // 用户序列号, 0-用户不存在， 非0-用户存在
}

message UpdatePasswordRequest {
  string old_password = 1;
  string new_password = 2;
}

message ResetPasswordRequest {
  string email = 1 [(validate.rules).string.email = true] ;
  string email_verify_code = 2;
  string email_verify_code_token = 3;
  string new_password = 4;
}

message GetProfileRequest {}

message UserProfile {
  string nickname = 1;  // 昵称
  string email = 2;     // 邮箱
  int32 status = 3;     // 状态
  string country = 4;   // 国家或地区, 不可修改
  string lang = 5;      // 语言
  uint32 birthday = 6;  // 生日
  float points = 7;     // 用户积分余额
  string device_id = 8; // 设备ID
  uint32 last_login_at = 9; // 最后登录时间
  uint32 created_at = 10; // 创建时间
  uint32 updated_at = 11; // 更新时间
}

message DetailProfile {
  uint64 user_id = 1;
  UserProfile profile = 2;
}

message UpdateProfileRequest {
  string nickname = 1; //  更新用户信息请求，字段为空或0， 则代表不更改。
  string lang = 2;     // 语言代码
  uint32 birthday = 3; // 生日， unix 秒级时间戳
}

message GetEmailVerifyCodeRequest {
  string email = 1 [(validate.rules).string.email = true]; // 邮箱
}

message GetEmailVerifyCodeResponse {
  string email_verify_code_token = 1; // 验证码令牌
  string email_verify_code = 2; // 验证码, 仅研发环境返回，生产环境不返回此值。
}
