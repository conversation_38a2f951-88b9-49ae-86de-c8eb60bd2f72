# PointService 使用文档

## 概述

`PointService` 是一个通过 dubbo-go v3 调用用户服务的积分充值服务。它封装了与用户服务的 `ChargePoints` 接口的通信逻辑。

## 功能特性

- 通过 dubbo-go v3 调用远程用户服务
- 自动参数验证
- 懒加载客户端初始化
- 完整的错误处理和日志记录
- 符合项目整体架构的依赖注入

## 接口定义

```go
type PointService interface {
    // ChargePoints 调用用户服务充值积分
    ChargePoints(ctx context.Context, points float32, reason string) error
}
```

## 配置要求

服务依赖 Nacos 注册中心，需要在配置文件中启用：

```yaml
# config.yaml 或 config.dev.yaml
nacos:
  enabled: true
  endpoints:
    - "127.0.0.1:8848"
  namespace: "payment-service"
  username: "nacos"
  password: "nacos"
```

## 使用示例

### 1. 在服务层中使用

```go
package service

import (
    "context"
    "payment-backend/internal/service"
)

type OrderService struct {
    pointService service.PointService
    // 其他依赖...
}

func (s *OrderService) ProcessPaymentSuccess(ctx context.Context, orderID string, amount float32) error {
    // 支付成功后，给用户充值积分
    points := amount * 0.1 // 10% 返积分
    reason := fmt.Sprintf("Payment success for order %s", orderID)
    
    if err := s.pointService.ChargePoints(ctx, points, reason); err != nil {
        // 记录错误，但不影响主流程
        log.Error("Failed to charge points", zap.Error(err))
    }
    
    return nil
}
```

### 2. 在处理器中使用

```go
package handler

import (
    "net/http"
    "payment-backend/internal/service"
)

type AdminHandler struct {
    pointService service.PointService
    // 其他依赖...
}

func (h *AdminHandler) ChargeUserPoints(w http.ResponseWriter, r *http.Request) {
    var req struct {
        Points float32 `json:"points"`
        Reason string  `json:"reason"`
    }
    
    if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
        http.Error(w, "Invalid request", http.StatusBadRequest)
        return
    }
    
    if err := h.pointService.ChargePoints(r.Context(), req.Points, req.Reason); err != nil {
        http.Error(w, err.Error(), http.StatusInternalServerError)
        return
    }
    
    w.WriteHeader(http.StatusOK)
    json.NewEncoder(w).Encode(map[string]string{"status": "success"})
}
```

### 3. 依赖注入配置

服务已经在 `internal/app/app.go` 中配置了依赖注入：

```go
// 在其他服务中注入 PointService
fx.Provide(
    func(pointService service.PointService, otherDeps...) *YourService {
        return NewYourService(pointService, otherDeps...)
    },
)
```

## 参数验证

服务会自动验证输入参数：

- `points`: 必须大于 0 且小于等于 1,000,000
- `reason`: 长度必须在 1-88 个字符之间

## 错误处理

服务可能返回以下类型的错误：

1. **配置错误**: Nacos 未启用或配置不正确
2. **连接错误**: 无法连接到用户服务
3. **参数错误**: 输入参数不符合要求
4. **业务错误**: 用户服务返回的业务错误

## 日志记录

服务会记录以下关键操作：

- 客户端初始化成功
- 积分充值请求和结果
- 错误信息

## 性能考虑

- 客户端使用懒加载，只在第一次调用时初始化
- 使用 `sync.Once` 确保客户端只初始化一次
- 支持并发调用

## 测试

运行单元测试：

```bash
go test ./internal/service -v -run TestPointService
```

## 注意事项

1. 确保 Nacos 注册中心正常运行
2. 确保用户服务已注册到 Nacos
3. 网络连通性要求：payment-service 能够访问 Nacos 和 user-service
4. 服务调用是同步的，建议在需要时考虑异步处理
