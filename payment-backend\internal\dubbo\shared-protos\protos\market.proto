syntax = "proto3";

package com.aibook.market.grpc;

option go_package = "./marketpb;marketpb";

import "google/api/annotations.proto";

service MarketService {
  // 共享绘本
  rpc ShareBook(ShareBookRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/market/share"
      body : "*"
    };
  }

  // 绘本详情
  rpc BookDetail(BookId) returns (BookInfo) {
    option (google.api.http) = {
      get : "/api/v1/market/book/{book_id}"
    };
  }

  // 绘本属性修改(如审核、发布、下架, 删除) -- 管理平台调用
  rpc BookModify(BookModifyRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/market/book/{book_id}"
      body : "*"
    };
  }

  // 操作日志查询 --- 管理平台调用
  rpc BookOptLogs(BookIdPage) returns (BookOptLogsResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/book/opt/logs"
    };
  }

  // 搜索绘本（按主题、按标题）， 按热度排序返回 --- 绘本终端调用
  rpc SearchBook(SearchBookRequest) returns (BookListPageResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/search"
    };
  }

  // 管理平台搜索绘本 --- 管理平台调用
  rpc AdminSearchBook(AdminSearchBookRequest) returns (BookListPageResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/admin/search"
    };
  }

  // 绘本主题 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
  rpc ThemeTopk(Empty) returns (ThemeListResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/topk"
    };
  }

  // 绘本主题下绘本 topK (按国家和年龄段筛选， 全量返回) --- 绘本终端调用
  rpc ThemeBookTopk(Empty) returns (BookList) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/book/topk"
    };
  }

  // 绘本主题下绘本列表(按国家和年龄段筛选， 全量返回) --- 绘本终端调用
  rpc GetThemeBooks(ThemeBooksPageRequest) returns (BookListPageResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/books"
    };
  }

  // 绘本主题列表(全量返回) --- 管理平台调用
  rpc GetThemeList(Empty) returns (ThemeDetailListResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/theme/list"
    };
  }

  // 推荐绘本查询(根据国家和年龄段筛选) --- 绘本终端调用
  rpc GetRecommendBooks(Empty) returns (BookList) {
    option (google.api.http) = {
      get : "/api/v1/market/recommend/books"
    };
  }

  // 查询绘本下载记录 --- 管理平台调用
  rpc GetBookDownloadRecord(UserIdPageRequest)
      returns (BookDownloadRecordResponse) {
    option (google.api.http) = {
      get : "/api/v1/market/book/download/record"
    };
  }

  // 同步下载记录 --- 绘本终端调用
  rpc SyncBookDownloadRecord(BookIdRequest) returns (Empty) {
    option (google.api.http) = {
      post : "/api/v1/market/book/download/record/sync"
    };
  }
}

message UserIdRequest { uint64 user_id = 1; }
message BookIdRequest { uint64 book_id = 1; }

message UserIdPageRequest {
  uint64 user_id = 1;
  PageRequest page = 2;
}

message PageRequest {
  uint32 page = 1;
  uint32 page_size = 2;
}

message PageResponse {
  uint32 total = 1;
  uint32 page = 2;
  uint32 page_size = 3;
}

message SearchBookRequest {
  PageRequest page = 1;
  string search_str = 2;
}

message AdminSearchBookRequest {
  PageRequest page = 1;
  uint32 order_by =
      2;            // 排序字段( 0-国家、1-主题、2-状态、3-年龄段、4-名称筛选)
  bool is_desc = 3; // 是否降序
  string search_by_title = 4;
  string search_status = 5;
  string search_by_description = 6;
  string search_by_age_range = 7;
  string search_by_country = 8;
  string search_by_recommend = 9; // 是否推荐(yes or no)
}

message BookListPageResponse {
  repeated BookInfo books = 1;
  PageResponse page = 2;
}

message BookList { repeated BookInfo books = 1; }

message OperateInfo {
  string operater = 1;        // 操作者
  string identity = 2;        // 身份
  string operate_type = 3;    // 操作类型(审核、发布、下架、删除等)
  string operate_desc = 4;    // 操作描述或结论(审批未通过的原因等)
  uint64 operate_book_id = 6; // 操作的绘本
  uint32 created_at = 5;      // 操作时间
}

message BookOptLogsResponse {
  repeated OperateInfo logs = 1;
  PageResponse page = 2;
}

message BookDownloadRecord {
  uint64 book_id = 1;
  uint64 user_id = 2;
  uint32 created_at = 3;
  string title = 4; // 绘本标题
}

message BookDownloadRecordResponse {
  repeated BookDownloadRecord records = 1;
  PageResponse page = 2;
}

message BookModifyRequest {
  uint64 book_id = 1;            // 绘本ID
  repeated uint32 theme_ids = 2; // 绘本主题, 空表示不修改
  string status = 3;             // 绘本状态, 空表示不修改
  string recommend_at =
      4; // 推荐时间, 空表示不修改， 0-表示不推荐，秒级时间戳-表示推荐
  OperateInfo operate_info = 5; // 操作信息

  // string title = 2;       // 绘本标题
  // string description = 3; // 绘本描述
  // string cover =
  //     4; // 绘本封面(需生先修改生成模块对应的封面图片，不建议修改此值)

  // string country = 6;            // 绘本国家, 国家代码
  // string age_range = 7;          // 绘本年龄段
}

message BookId { uint64 book_id = 1; }
message BookIdPage {
  uint64 book_id = 1;
  PageRequest page = 2;
}

message ThemeBooksPageRequest {
  uint32 theme_id = 1; // 绘本主题ID
  PageRequest page = 2;
}

message BookInfo {
  uint64 book_id = 1;          // 绘本ID
  string country = 2;          // 绘本国家
  string lang = 3;             // 绘本语言
  string title = 4;            // 绘本标题
  string description = 5;      // 绘本描述
  string cover = 6;            // 绘本封面
  string type = 7;             // 绘本类型：图文、动态
  string age_range = 8;        // 绘本年龄段
  uint32 recommended_at = 9;   // 绘本推荐时间, 值为0表示不推荐
  uint32 downloads = 10;       // 绘本下载次数
  uint64 from_user_id = 11;    // 绘本作者ID
  string status = 12;          // 绘本状态
  repeated string themes = 13; // 绘本主题
  uint32 created_at = 14;      // 绘本进入绘本市场时间
  uint32 updated_at = 15;      // 最后更新时间
}

message ThemeBookTopkRequest { uint64 theme_id = 1; }

message ThemeBookTopkResponse {
  repeated BookInfo books = 1; // 绘本数组
}

message ThemeListResponse {
  repeated Theme themes = 1; // 绘本主题数组
  PageResponse page = 2;     // 分页信息
}

message Theme {
  uint64 theme_id = 1;        // 绘本主题ID
  string name = 2;            // 绘本主题名称
  string description = 3;     // 绘本主题描述
  string cover = 4;           // 绘本主题封面
  uint32 recommend_score = 5; // 绘本主题推荐分数
  uint32 book_count = 6;      // 绘本数量
}


message ThemeDetailListResponse {
  repeated Theme themes = 1; // 绘本主题数组
  PageResponse page = 2;     // 分页信息
}


message Empty {}

message ShareBookRequest {
  uint64 book_id = 1; // 绘本ID
}
