// Code generated by protoc-gen-triple. DO NOT EDIT.
//
// Source: protos/market.proto
package marketpb

import (
	"context"
)

import (
	"dubbo.apache.org/dubbo-go/v3"
	"dubbo.apache.org/dubbo-go/v3/client"
	"dubbo.apache.org/dubbo-go/v3/common"
	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"dubbo.apache.org/dubbo-go/v3/protocol/triple/triple_protocol"
	"dubbo.apache.org/dubbo-go/v3/server"
)

// This is a compile-time assertion to ensure that this generated file and the Triple package
// are compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of Triple newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of Triple or updating the Triple
// version compiled into your binary.
const _ = triple_protocol.IsAtLeastVersion0_1_0

const (
	// MarketServiceName is the fully-qualified name of the MarketService service.
	MarketServiceName = "com.aibook.market.grpc.MarketService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// MarketServiceShareBookProcedure is the fully-qualified name of the MarketService's ShareBook RPC.
	MarketServiceShareBookProcedure = "/com.aibook.market.grpc.MarketService/ShareBook"
	// MarketServiceBookDetailProcedure is the fully-qualified name of the MarketService's BookDetail RPC.
	MarketServiceBookDetailProcedure = "/com.aibook.market.grpc.MarketService/BookDetail"
	// MarketServiceBookModifyProcedure is the fully-qualified name of the MarketService's BookModify RPC.
	MarketServiceBookModifyProcedure = "/com.aibook.market.grpc.MarketService/BookModify"
	// MarketServiceBookOptLogsProcedure is the fully-qualified name of the MarketService's BookOptLogs RPC.
	MarketServiceBookOptLogsProcedure = "/com.aibook.market.grpc.MarketService/BookOptLogs"
	// MarketServiceSearchBookProcedure is the fully-qualified name of the MarketService's SearchBook RPC.
	MarketServiceSearchBookProcedure = "/com.aibook.market.grpc.MarketService/SearchBook"
	// MarketServiceAdminSearchBookProcedure is the fully-qualified name of the MarketService's AdminSearchBook RPC.
	MarketServiceAdminSearchBookProcedure = "/com.aibook.market.grpc.MarketService/AdminSearchBook"
	// MarketServiceThemeTopkProcedure is the fully-qualified name of the MarketService's ThemeTopk RPC.
	MarketServiceThemeTopkProcedure = "/com.aibook.market.grpc.MarketService/ThemeTopk"
	// MarketServiceThemeBookTopkProcedure is the fully-qualified name of the MarketService's ThemeBookTopk RPC.
	MarketServiceThemeBookTopkProcedure = "/com.aibook.market.grpc.MarketService/ThemeBookTopk"
	// MarketServiceGetThemeBooksProcedure is the fully-qualified name of the MarketService's GetThemeBooks RPC.
	MarketServiceGetThemeBooksProcedure = "/com.aibook.market.grpc.MarketService/GetThemeBooks"
	// MarketServiceGetThemeListProcedure is the fully-qualified name of the MarketService's GetThemeList RPC.
	MarketServiceGetThemeListProcedure = "/com.aibook.market.grpc.MarketService/GetThemeList"
	// MarketServiceGetRecommendBooksProcedure is the fully-qualified name of the MarketService's GetRecommendBooks RPC.
	MarketServiceGetRecommendBooksProcedure = "/com.aibook.market.grpc.MarketService/GetRecommendBooks"
	// MarketServiceGetBookDownloadRecordProcedure is the fully-qualified name of the MarketService's GetBookDownloadRecord RPC.
	MarketServiceGetBookDownloadRecordProcedure = "/com.aibook.market.grpc.MarketService/GetBookDownloadRecord"
	// MarketServiceSyncBookDownloadRecordProcedure is the fully-qualified name of the MarketService's SyncBookDownloadRecord RPC.
	MarketServiceSyncBookDownloadRecordProcedure = "/com.aibook.market.grpc.MarketService/SyncBookDownloadRecord"
)

var (
	_ MarketService = (*MarketServiceImpl)(nil)
)

// MarketService is a client for the com.aibook.market.grpc.MarketService service.
type MarketService interface {
	ShareBook(ctx context.Context, req *ShareBookRequest, opts ...client.CallOption) (*Empty, error)
	BookDetail(ctx context.Context, req *BookId, opts ...client.CallOption) (*BookInfo, error)
	BookModify(ctx context.Context, req *BookModifyRequest, opts ...client.CallOption) (*Empty, error)
	BookOptLogs(ctx context.Context, req *BookIdPage, opts ...client.CallOption) (*BookOptLogsResponse, error)
	SearchBook(ctx context.Context, req *SearchBookRequest, opts ...client.CallOption) (*BookListPageResponse, error)
	AdminSearchBook(ctx context.Context, req *AdminSearchBookRequest, opts ...client.CallOption) (*BookListPageResponse, error)
	ThemeTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeListResponse, error)
	ThemeBookTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*BookList, error)
	GetThemeBooks(ctx context.Context, req *ThemeBooksPageRequest, opts ...client.CallOption) (*BookListPageResponse, error)
	GetThemeList(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeDetailListResponse, error)
	GetRecommendBooks(ctx context.Context, req *Empty, opts ...client.CallOption) (*BookList, error)
	GetBookDownloadRecord(ctx context.Context, req *UserIdPageRequest, opts ...client.CallOption) (*BookDownloadRecordResponse, error)
	SyncBookDownloadRecord(ctx context.Context, req *BookIdRequest, opts ...client.CallOption) (*Empty, error)
}

// NewMarketService constructs a client for the marketpb.MarketService service.
func NewMarketService(cli *client.Client, opts ...client.ReferenceOption) (MarketService, error) {
	conn, err := cli.DialWithInfo("com.aibook.market.grpc.MarketService", &MarketService_ClientInfo, opts...)
	if err != nil {
		return nil, err
	}
	return &MarketServiceImpl{
		conn: conn,
	}, nil
}

func SetConsumerMarketService(srv common.RPCService) {
	dubbo.SetConsumerServiceWithInfo(srv, &MarketService_ClientInfo)
}

// MarketServiceImpl implements MarketService.
type MarketServiceImpl struct {
	conn *client.Connection
}

func (c *MarketServiceImpl) ShareBook(ctx context.Context, req *ShareBookRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ShareBook", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) BookDetail(ctx context.Context, req *BookId, opts ...client.CallOption) (*BookInfo, error) {
	resp := new(BookInfo)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BookDetail", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) BookModify(ctx context.Context, req *BookModifyRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BookModify", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) BookOptLogs(ctx context.Context, req *BookIdPage, opts ...client.CallOption) (*BookOptLogsResponse, error) {
	resp := new(BookOptLogsResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "BookOptLogs", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) SearchBook(ctx context.Context, req *SearchBookRequest, opts ...client.CallOption) (*BookListPageResponse, error) {
	resp := new(BookListPageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "SearchBook", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) AdminSearchBook(ctx context.Context, req *AdminSearchBookRequest, opts ...client.CallOption) (*BookListPageResponse, error) {
	resp := new(BookListPageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "AdminSearchBook", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) ThemeTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeListResponse, error) {
	resp := new(ThemeListResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ThemeTopk", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) ThemeBookTopk(ctx context.Context, req *Empty, opts ...client.CallOption) (*BookList, error) {
	resp := new(BookList)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "ThemeBookTopk", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) GetThemeBooks(ctx context.Context, req *ThemeBooksPageRequest, opts ...client.CallOption) (*BookListPageResponse, error) {
	resp := new(BookListPageResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetThemeBooks", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) GetThemeList(ctx context.Context, req *Empty, opts ...client.CallOption) (*ThemeDetailListResponse, error) {
	resp := new(ThemeDetailListResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetThemeList", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) GetRecommendBooks(ctx context.Context, req *Empty, opts ...client.CallOption) (*BookList, error) {
	resp := new(BookList)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetRecommendBooks", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) GetBookDownloadRecord(ctx context.Context, req *UserIdPageRequest, opts ...client.CallOption) (*BookDownloadRecordResponse, error) {
	resp := new(BookDownloadRecordResponse)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "GetBookDownloadRecord", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

func (c *MarketServiceImpl) SyncBookDownloadRecord(ctx context.Context, req *BookIdRequest, opts ...client.CallOption) (*Empty, error) {
	resp := new(Empty)
	if err := c.conn.CallUnary(ctx, []interface{}{req}, resp, "SyncBookDownloadRecord", opts...); err != nil {
		return nil, err
	}
	return resp, nil
}

var MarketService_ClientInfo = client.ClientInfo{
	InterfaceName: "com.aibook.market.grpc.MarketService",
	MethodNames:   []string{"ShareBook", "BookDetail", "BookModify", "BookOptLogs", "SearchBook", "AdminSearchBook", "ThemeTopk", "ThemeBookTopk", "GetThemeBooks", "GetThemeList", "GetRecommendBooks", "GetBookDownloadRecord", "SyncBookDownloadRecord"},
	ConnectionInjectFunc: func(dubboCliRaw interface{}, conn *client.Connection) {
		dubboCli := dubboCliRaw.(*MarketServiceImpl)
		dubboCli.conn = conn
	},
}

// MarketServiceHandler is an implementation of the com.aibook.market.grpc.MarketService service.
type MarketServiceHandler interface {
	ShareBook(context.Context, *ShareBookRequest) (*Empty, error)
	BookDetail(context.Context, *BookId) (*BookInfo, error)
	BookModify(context.Context, *BookModifyRequest) (*Empty, error)
	BookOptLogs(context.Context, *BookIdPage) (*BookOptLogsResponse, error)
	SearchBook(context.Context, *SearchBookRequest) (*BookListPageResponse, error)
	AdminSearchBook(context.Context, *AdminSearchBookRequest) (*BookListPageResponse, error)
	ThemeTopk(context.Context, *Empty) (*ThemeListResponse, error)
	ThemeBookTopk(context.Context, *Empty) (*BookList, error)
	GetThemeBooks(context.Context, *ThemeBooksPageRequest) (*BookListPageResponse, error)
	GetThemeList(context.Context, *Empty) (*ThemeDetailListResponse, error)
	GetRecommendBooks(context.Context, *Empty) (*BookList, error)
	GetBookDownloadRecord(context.Context, *UserIdPageRequest) (*BookDownloadRecordResponse, error)
	SyncBookDownloadRecord(context.Context, *BookIdRequest) (*Empty, error)
}

func RegisterMarketServiceHandler(srv *server.Server, hdlr MarketServiceHandler, opts ...server.ServiceOption) error {
	return srv.Register(hdlr, &MarketService_ServiceInfo, opts...)
}

func SetProviderMarketService(srv common.RPCService) {
	dubbo.SetProviderServiceWithInfo(srv, &MarketService_ServiceInfo)
}

var MarketService_ServiceInfo = server.ServiceInfo{
	InterfaceName: "com.aibook.market.grpc.MarketService",
	ServiceType:   (*MarketServiceHandler)(nil),
	Methods: []server.MethodInfo{
		{
			Name: "ShareBook",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ShareBookRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ShareBookRequest)
				res, err := handler.(MarketServiceHandler).ShareBook(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BookDetail",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookId)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookId)
				res, err := handler.(MarketServiceHandler).BookDetail(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BookModify",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookModifyRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookModifyRequest)
				res, err := handler.(MarketServiceHandler).BookModify(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "BookOptLogs",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookIdPage)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookIdPage)
				res, err := handler.(MarketServiceHandler).BookOptLogs(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "SearchBook",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(SearchBookRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*SearchBookRequest)
				res, err := handler.(MarketServiceHandler).SearchBook(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "AdminSearchBook",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(AdminSearchBookRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*AdminSearchBookRequest)
				res, err := handler.(MarketServiceHandler).AdminSearchBook(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ThemeTopk",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(MarketServiceHandler).ThemeTopk(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "ThemeBookTopk",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(MarketServiceHandler).ThemeBookTopk(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetThemeBooks",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(ThemeBooksPageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*ThemeBooksPageRequest)
				res, err := handler.(MarketServiceHandler).GetThemeBooks(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetThemeList",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(MarketServiceHandler).GetThemeList(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetRecommendBooks",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(Empty)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*Empty)
				res, err := handler.(MarketServiceHandler).GetRecommendBooks(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "GetBookDownloadRecord",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(UserIdPageRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*UserIdPageRequest)
				res, err := handler.(MarketServiceHandler).GetBookDownloadRecord(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
		{
			Name: "SyncBookDownloadRecord",
			Type: constant.CallUnary,
			ReqInitFunc: func() interface{} {
				return new(BookIdRequest)
			},
			MethodFunc: func(ctx context.Context, args []interface{}, handler interface{}) (interface{}, error) {
				req := args[0].(*BookIdRequest)
				res, err := handler.(MarketServiceHandler).SyncBookDownloadRecord(ctx, req)
				if err != nil {
					return nil, err
				}
				return triple_protocol.NewResponse(res), nil
			},
		},
	},
}
