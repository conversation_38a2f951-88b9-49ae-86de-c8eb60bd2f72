package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	"dubbo.apache.org/dubbo-go/v3/common/constant"
	"go.uber.org/zap"

	"payment-backend/internal/config"
	"payment-backend/internal/logger"
)

// PointServiceHTTP HTTP方式积分服务接口
type PointServiceHTTP interface {
	WithUserContext(ctx context.Context, userId string, country, traceId string) context.Context
	// ChargePoints 通过HTTP调用用户服务充值积分
	ChargePoints(ctx context.Context, points float32, reason string) error
}

// pointServiceHTTP HTTP方式积分服务实现
type pointServiceHTTP struct {
	config     *config.Config
	logger     logger.Logger
	httpClient *http.Client
	baseURL    string
	clientMu   sync.Mutex
}

// ChargePointsHTTPRequest HTTP充值积分请求
type ChargePointsHTTPRequest struct {
	Points float32 `json:"points"` // 积分数
	Reason string  `json:"reason"` // 充值原因
}

// ChargePointsHTTPResponse HTTP充值积分响应
type ChargePointsHTTPResponse struct {
	// 根据用户服务的实际响应结构调整
	Success bool   `json:"success,omitempty"`
	Message string `json:"message,omitempty"`
}

// NewPointServiceHTTP 创建HTTP方式积分服务
func NewPointServiceHTTP(config *config.Config, logger logger.Logger) PointServiceHTTP {
	return &pointServiceHTTP{
		config: config,
		logger: logger,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// initHTTPClient 初始化HTTP客户端（懒加载）
func (s *pointServiceHTTP) initHTTPClient() error {
	s.clientMu.Lock()
	defer s.clientMu.Unlock()

	// 如果已经初始化过，直接返回
	if s.baseURL != "" {
		return nil
	}

	// 检查 Nacos 是否启用
	if !s.config.Nacos.Enabled {
		return fmt.Errorf("nacos is not enabled, cannot get user service HTTP address")
	}

	// 从 Nacos 配置中心获取用户服务的 HTTP 地址
	// 这里需要根据实际的配置中心配置来获取
	// 假设配置中心中有 user_service.http.base_url 配置项
	baseURL, err := s.getUserServiceBaseURLFromNacos()
	if err != nil {
		s.logger.Error("Failed to get user service base URL from Nacos", zap.Error(err))
		return fmt.Errorf("failed to get user service base URL: %w", err)
	}

	s.baseURL = baseURL
	s.logger.Info("HTTP client initialized successfully", zap.String("base_url", s.baseURL))

	return nil
}

// getUserServiceBaseURLFromNacos 从Nacos配置中心获取用户服务的HTTP地址
func (s *pointServiceHTTP) getUserServiceBaseURLFromNacos() (string, error) {
	// 方案1: 从环境变量获取（优先级最高）
	if baseURL := s.getBaseURLFromEnv(); baseURL != "" {
		s.logger.Info("Using user service base URL from environment variable",
			zap.String("base_url", baseURL))
		return baseURL, nil
	}

	// 方案2: 从Nacos配置中心获取
	if baseURL, err := s.getBaseURLFromNacosConfig(); err == nil && baseURL != "" {
		s.logger.Info("Using user service base URL from Nacos config center",
			zap.String("base_url", baseURL))
		return baseURL, nil
	} else if err != nil {
		s.logger.Warn("Failed to get base URL from Nacos config center", zap.Error(err))
	}

	// 方案3: 使用默认地址（开发环境）
	defaultBaseURL := "http://localhost:8081" // 假设用户服务运行在8081端口
	s.logger.Warn("Using default user service base URL, should configure in Nacos or environment variable in production",
		zap.String("default_base_url", defaultBaseURL))

	return defaultBaseURL, nil
}

// getBaseURLFromEnv 从环境变量获取用户服务地址
func (s *pointServiceHTTP) getBaseURLFromEnv() string {
	// 可以通过环境变量 USER_SERVICE_HTTP_BASE_URL 设置
	// 例如: export USER_SERVICE_HTTP_BASE_URL=http://user-service:8081
	return "" // 这里可以使用 os.Getenv("USER_SERVICE_HTTP_BASE_URL")
}

// getBaseURLFromNacosConfig 从Nacos配置中心获取用户服务地址
func (s *pointServiceHTTP) getBaseURLFromNacosConfig() (string, error) {
	// TODO: 实现从Nacos配置中心获取配置的逻辑
	// 这需要根据实际的Nacos配置结构来实现

	// 示例实现框架：
	// 1. 创建Nacos配置客户端
	// 2. 获取用户服务配置
	// 3. 解析配置获取HTTP地址

	/*
		// 创建Nacos配置客户端
		nacosClient, err := s.createNacosConfigClient()
		if err != nil {
			return "", fmt.Errorf("failed to create nacos config client: %w", err)
		}

		// 获取用户服务配置
		configContent, err := nacosClient.GetConfig(vo.ConfigParam{
			DataId: "user-service-config",
			Group:  "DEFAULT_GROUP",
		})
		if err != nil {
			return "", fmt.Errorf("failed to get config from nacos: %w", err)
		}

		// 解析配置
		var userServiceConfig struct {
			HTTP struct {
				BaseURL string `yaml:"base_url"`
			} `yaml:"http"`
		}

		if err := yaml.Unmarshal([]byte(configContent), &userServiceConfig); err != nil {
			return "", fmt.Errorf("failed to unmarshal config: %w", err)
		}

		if userServiceConfig.HTTP.BaseURL == "" {
			return "", fmt.Errorf("user service base URL not configured in nacos")
		}

		return userServiceConfig.HTTP.BaseURL, nil
	*/

	// 临时返回空，表示从Nacos获取失败
	return "", fmt.Errorf("nacos config center integration not implemented yet")
}

func (s *pointServiceHTTP) WithUserContext(ctx context.Context, userIdStr string, country, traceId string) context.Context {
	v := map[string]any{"x-user-id": []string{userIdStr},
		"x-country":  []string{country},
		"x-trace-id": []string{traceId}}
	return context.WithValue(ctx, constant.AttachmentKey, v)
}

// ChargePoints 通过HTTP调用用户服务充值积分
func (s *pointServiceHTTP) ChargePoints(ctx context.Context, points float32, reason string) error {
	// 验证参数
	if points <= 0 {
		return fmt.Errorf("points must be greater than 0")
	}
	if points > 100000000 {
		return fmt.Errorf("points must be less than or equal to 100000000")
	}
	if len(reason) == 0 || len(reason) > 1024 {
		return fmt.Errorf("reason length must be between 1 and 1024 characters")
	}

	// 初始化HTTP客户端
	if err := s.initHTTPClient(); err != nil {
		return fmt.Errorf("failed to initialize HTTP client: %w", err)
	}

	// 构建请求
	req := &ChargePointsHTTPRequest{
		Points: points,
		Reason: reason,
	}

	s.logger.Info("Calling user service ChargePoints via HTTP",
		zap.Float32("points", points),
		zap.String("reason", reason),
		zap.String("base_url", s.baseURL))

	// 发送HTTP请求
	if err := s.sendChargePointsRequest(ctx, req); err != nil {
		s.logger.Error("Failed to charge points via HTTP",
			zap.Float32("points", points),
			zap.String("reason", reason),
			zap.Error(err))
		return fmt.Errorf("failed to charge points via HTTP: %w", err)
	}

	s.logger.Info("Points charged successfully via HTTP",
		zap.Float32("points", points),
		zap.String("reason", reason))

	return nil
}

// sendChargePointsRequest 发送充值积分HTTP请求
func (s *pointServiceHTTP) sendChargePointsRequest(ctx context.Context, req *ChargePointsHTTPRequest) error {
	// 构建请求URL - 根据protobuf定义，HTTP路径是 /api/v1/user/charge
	url := s.baseURL + "/api/v1/user/charge"

	// 序列化请求体
	jsonData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")

	// 从上下文中获取用户信息并设置到请求头
	if attachments := ctx.Value(constant.AttachmentKey); attachments != nil {
		if attachmentMap, ok := attachments.(map[string]any); ok {
			if userIds, exists := attachmentMap["x-user-id"]; exists {
				if userIdSlice, ok := userIds.([]string); ok && len(userIdSlice) > 0 {
					httpReq.Header.Set("x-user-id", userIdSlice[0])
				}
			}
			if countries, exists := attachmentMap["x-country"]; exists {
				if countrySlice, ok := countries.([]string); ok && len(countrySlice) > 0 {
					httpReq.Header.Set("x-country", countrySlice[0])
				}
			}
			if traceIds, exists := attachmentMap["x-trace-id"]; exists {
				if traceIdSlice, ok := traceIds.([]string); ok && len(traceIdSlice) > 0 {
					httpReq.Header.Set("x-trace-id", traceIdSlice[0])
				}
			}
		}
	}

	// 发送请求
	resp, err := s.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应（根据用户服务的实际响应格式调整）
	var response ChargePointsHTTPResponse
	if err := json.Unmarshal(body, &response); err != nil {
		// 如果解析失败，可能是因为响应格式不同，记录日志但不返回错误
		s.logger.Warn("Failed to unmarshal response, but HTTP status is OK",
			zap.String("response_body", string(body)),
			zap.Error(err))
	}

	s.logger.Info("HTTP request completed successfully",
		zap.Int("status_code", resp.StatusCode),
		zap.String("response_body", string(body)))

	return nil
}
