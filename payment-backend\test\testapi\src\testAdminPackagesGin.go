package main

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

// testAdminAddPackagesGin 测试内网创建流量包接口 (Gin HTTP)
func testAdminAddPackagesGin(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Add Packages (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/store-service/packages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	// 构造创建流量包请求体
	requestBody := CreatePackageRequest{
		PackageName:     "测试流量包GIN",
		PackageDesc:     "用于API测试的流量包",
		PackageType:     "traffic",
		Entitlement:     100,
		EntitlementDesc: "100GB流量",
		OriginalPrice:   19.99,
		DiscountPrice:   nil,
		DiscountDesc:    "",
		SaleStatus:      "on_sale",
		Currency:        "USD",
		Country:         "US",
		Extra1:          "test",
		Extra2:          "api",
		Extra3:          100,
		Extra4:          30,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("PUT", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 || resp.StatusCode == 201 {

		// 从响应中解析出创建的流量包ID
		packageID := ""
		var packageResp CreatePackageResponse
		if err := json.Unmarshal(body, &packageResp); err == nil {
			packageID = packageResp.PackageID
		}

		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package created successfully via Gin HTTP",
			Duration: duration,
			CtxMapString: map[string]string{"package_id_to_update": packageID,
				"package_id_to_buy": packageID},
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// 通过 GIN 接口获取管理员可以查询到的流量包总数
func getAdminAllPackagesCountGin(config *EnvConfig) (int, error) {
	url := config.LocalGinURL + "/api/v1/pay-service/admin/store-service/packages?limit=1&offset=0"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return 0, err
	}
	if resp.StatusCode != 200 {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return 0, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	var listResp AdminListPackagesResponse
	if err := json.Unmarshal(body, &listResp); err != nil {
		if detailLogger != nil {

			detailLogger.Printf("Failed to unmarshal response: %v", err)
		}
		return 0, err
	}

	return int(listResp.Pagination.Total), nil
}

// testAdminListAllPackagesGin 测试内网获取所有流量包接口 (Gin HTTP)
func testAdminListAllPackagesGin(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin List All Packages (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/store-service/packages?limit=50&offset=0"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
	}

	resp, err := makeHTTPRequest("GET", url, headers, nil)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Admin packages retrieved successfully via Gin HTTP",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminUpdatePackagesGin 测试内网更新流量包接口 (Gin HTTP)
func testAdminUpdatePackagesGin(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Update Packages (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/store-service/packages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	// 构造更新流量包请求体 - 需要先有一个存在的package_id
	res, package_id_to_update, err := GetCtxValue("package_id_to_update", results, testName, start)
	if err != nil {
		return *res
	}

	newPrice := 24.99
	packageName := "更新后的测试流量包"
	originalPrice := newPrice
	requestBody := UpdatePackageRequest{
		PackageID:     package_id_to_update,
		PackageName:   &packageName,
		OriginalPrice: &originalPrice,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {

		return TestResult{
			TestName:     testName,
			Success:      true,
			Message:      "Package updated successfully via Gin HTTP",
			Duration:     duration,
			CtxMapString: map[string]string{"package_id_to_delete": package_id_to_update},
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminDeletePackagesGin 测试内网删除流量包接口 (Gin HTTP)
func testAdminDeletePackagesGin(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Delete Packages (Internal Gin HTTP)"

	url := config.LocalGinURL + "/api/v1/pay-service/admin/store-service/packages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
		"x-user-id":  "admin123",
		"x-role":     "admin",
	}

	preCount, err := getAdminAllPackagesCountGin(config)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to get package count: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to get package count: %v", err),
			Duration: time.Since(start),
		}
	}

	// 构造删除流量包请求体
	res, package_id_to_delete, err := GetCtxValue("package_id_to_delete", results, testName, start)
	if err != nil {
		return *res
	}

	requestBody := DeletePackageRequest{
		PackageID: package_id_to_delete,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("DELETE", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("  %s: Package deleted successfully\n", testName)

		afterCount, err := getAdminAllPackagesCountGin(config)
		if err != nil {
			if detailLogger != nil {
				detailLogger.Printf("Failed to get package count: %v", err)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  fmt.Sprintf("Failed to get package count: %v", err),
				Duration: time.Since(start),
			}
		}

		fmt.Printf("  %s: afterCount:%v, preCount:%v\n", testName, afterCount, preCount)

		if afterCount != preCount-1 {
			if detailLogger != nil {
				detailLogger.Printf("Package count not match, preCount=%d, afterCount=%d", preCount, afterCount)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  fmt.Sprintf("Package count not match, preCount=%d, afterCount=%d", preCount, afterCount),
				Duration: time.Since(start),
			}
		}

		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package deleted successfully via Gin HTTP",
			Duration: duration,
		}

	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
