// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: protos/order.proto

package paymentpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProductSnapshot struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserEmail     string                 `protobuf:"bytes,1,opt,name=user_email,json=userEmail,proto3" json:"user_email,omitempty"`       // 用户邮箱
	Country       string                 `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`                            // 订单国家
	PackageType   string                 `protobuf:"bytes,3,opt,name=package_type,json=packageType,proto3" json:"package_type,omitempty"` // 流量包类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProductSnapshot) Reset() {
	*x = ProductSnapshot{}
	mi := &file_protos_order_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProductSnapshot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductSnapshot) ProtoMessage() {}

func (x *ProductSnapshot) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductSnapshot.ProtoReflect.Descriptor instead.
func (*ProductSnapshot) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{0}
}

func (x *ProductSnapshot) GetUserEmail() string {
	if x != nil {
		return x.UserEmail
	}
	return ""
}

func (x *ProductSnapshot) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *ProductSnapshot) GetPackageType() string {
	if x != nil {
		return x.PackageType
	}
	return ""
}

// 订单信息
type Order struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Id                  uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                  // 自增主键 ID
	OrderId             string                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`                                          // 订单 ID（业务主键）
	UserId              string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`                                             // 用户 ID
	ProductId           string                 `protobuf:"bytes,4,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`                                    // 产品 ID
	ProductDesc         string                 `protobuf:"bytes,5,opt,name=product_desc,json=productDesc,proto3" json:"product_desc,omitempty"`                              // 产品描述.
	ProductSnapshot     string                 `protobuf:"bytes,6,opt,name=product_snapshot,json=productSnapshot,proto3" json:"product_snapshot,omitempty"`                  // 产品快照。对应订单中的 ProductSnapshot 字段。 ProductSnapshot 存储的是 ProductSnapshot 结构 json 序列化之后再 base64 编码得到的字符串。
	PriceId             string                 `protobuf:"bytes,7,opt,name=price_id,json=priceId,proto3" json:"price_id,omitempty"`                                          // 价格 ID
	Quantity            uint32                 `protobuf:"varint,8,opt,name=quantity,proto3" json:"quantity,omitempty"`                                                      // 购买数量
	Amount              float64                `protobuf:"fixed64,9,opt,name=amount,proto3" json:"amount,omitempty"`                                                         // 订单总金额（含税/手续费）
	NetAmount           float64                `protobuf:"fixed64,10,opt,name=net_amount,json=netAmount,proto3" json:"net_amount,omitempty"`                                 // 净金额（去税/手续费）
	Currency            string                 `protobuf:"bytes,11,opt,name=currency,proto3" json:"currency,omitempty"`                                                      // 币种（ISO-4217，如 USD）
	PayStatus           string                 `protobuf:"bytes,12,opt,name=pay_status,json=payStatus,proto3" json:"pay_status,omitempty"`                                   // 支付状态（如 created、paid、succeeded 等）
	PayRet              string                 `protobuf:"bytes,13,opt,name=pay_ret,json=payRet,proto3" json:"pay_ret,omitempty"`                                            // 支付错误信息
	PayedMethod         string                 `protobuf:"bytes,14,opt,name=payed_method,json=payedMethod,proto3" json:"payed_method,omitempty"`                             // 支付方式（如 card、paypal 等）
	PspProvider         string                 `protobuf:"bytes,15,opt,name=psp_provider,json=pspProvider,proto3" json:"psp_provider,omitempty"`                             // 支付服务商名称（PSP，如 stripe、paypal）
	CardNumber          string                 `protobuf:"bytes,16,opt,name=card_number,json=cardNumber,proto3" json:"card_number,omitempty"`                                // 支付卡尾号
	PayedAt             *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=payed_at,json=payedAt,proto3,oneof" json:"payed_at,omitempty"`                                   // 支付时间
	RefundStatus        string                 `protobuf:"bytes,18,opt,name=refund_status,json=refundStatus,proto3" json:"refund_status,omitempty"`                          // 退款状态（如 none、requested、succeeded）
	RefundedAt          *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=refunded_at,json=refundedAt,proto3,oneof" json:"refunded_at,omitempty"`                          // 退款时间
	PspPaymentId        string                 `protobuf:"bytes,20,opt,name=psp_payment_id,json=pspPaymentId,proto3" json:"psp_payment_id,omitempty"`                        // 支付平台生成的支付 ID
	PspPaymentIntentId  string                 `protobuf:"bytes,21,opt,name=psp_payment_intent_id,json=pspPaymentIntentId,proto3" json:"psp_payment_intent_id,omitempty"`    // PSP Intent ID
	PspPaymentRefundId  string                 `protobuf:"bytes,22,opt,name=psp_payment_refund_id,json=pspPaymentRefundId,proto3" json:"psp_payment_refund_id,omitempty"`    // PSP 最后一次退款的 ID
	PspPaymentRefundRet string                 `protobuf:"bytes,23,opt,name=psp_payment_refund_ret,json=pspPaymentRefundRet,proto3" json:"psp_payment_refund_ret,omitempty"` // PSP 最后一次退款的结果或失败原因
	PspCustomerId       string                 `protobuf:"bytes,24,opt,name=psp_customer_id,json=pspCustomerId,proto3" json:"psp_customer_id,omitempty"`                     // 支付平台生成的客户 ID
	PspCustomerEmail    string                 `protobuf:"bytes,25,opt,name=psp_customer_email,json=pspCustomerEmail,proto3" json:"psp_customer_email,omitempty"`            // 支付平台客户的邮箱
	PspSubscriptionId   string                 `protobuf:"bytes,26,opt,name=psp_subscription_id,json=pspSubscriptionId,proto3" json:"psp_subscription_id,omitempty"`         // 支付平台的订阅 ID
	CreatedAt           *timestamppb.Timestamp `protobuf:"bytes,27,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`                                   // 创建时间
	UpdatedAt           *timestamppb.Timestamp `protobuf:"bytes,28,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`                                   // 更新时间
	Deleted             bool                   `protobuf:"varint,29,opt,name=deleted,proto3" json:"deleted,omitempty"`                                                       // 是否被逻辑删除（true 表示删除）
	DeletedAt           *timestamppb.Timestamp `protobuf:"bytes,30,opt,name=deleted_at,json=deletedAt,proto3,oneof" json:"deleted_at,omitempty"`                             // 删除时间（逻辑删除）
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Order) Reset() {
	*x = Order{}
	mi := &file_protos_order_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{1}
}

func (x *Order) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Order) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *Order) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Order) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *Order) GetProductDesc() string {
	if x != nil {
		return x.ProductDesc
	}
	return ""
}

func (x *Order) GetProductSnapshot() string {
	if x != nil {
		return x.ProductSnapshot
	}
	return ""
}

func (x *Order) GetPriceId() string {
	if x != nil {
		return x.PriceId
	}
	return ""
}

func (x *Order) GetQuantity() uint32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *Order) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *Order) GetNetAmount() float64 {
	if x != nil {
		return x.NetAmount
	}
	return 0
}

func (x *Order) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Order) GetPayStatus() string {
	if x != nil {
		return x.PayStatus
	}
	return ""
}

func (x *Order) GetPayRet() string {
	if x != nil {
		return x.PayRet
	}
	return ""
}

func (x *Order) GetPayedMethod() string {
	if x != nil {
		return x.PayedMethod
	}
	return ""
}

func (x *Order) GetPspProvider() string {
	if x != nil {
		return x.PspProvider
	}
	return ""
}

func (x *Order) GetCardNumber() string {
	if x != nil {
		return x.CardNumber
	}
	return ""
}

func (x *Order) GetPayedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.PayedAt
	}
	return nil
}

func (x *Order) GetRefundStatus() string {
	if x != nil {
		return x.RefundStatus
	}
	return ""
}

func (x *Order) GetRefundedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RefundedAt
	}
	return nil
}

func (x *Order) GetPspPaymentId() string {
	if x != nil {
		return x.PspPaymentId
	}
	return ""
}

func (x *Order) GetPspPaymentIntentId() string {
	if x != nil {
		return x.PspPaymentIntentId
	}
	return ""
}

func (x *Order) GetPspPaymentRefundId() string {
	if x != nil {
		return x.PspPaymentRefundId
	}
	return ""
}

func (x *Order) GetPspPaymentRefundRet() string {
	if x != nil {
		return x.PspPaymentRefundRet
	}
	return ""
}

func (x *Order) GetPspCustomerId() string {
	if x != nil {
		return x.PspCustomerId
	}
	return ""
}

func (x *Order) GetPspCustomerEmail() string {
	if x != nil {
		return x.PspCustomerEmail
	}
	return ""
}

func (x *Order) GetPspSubscriptionId() string {
	if x != nil {
		return x.PspSubscriptionId
	}
	return ""
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Order) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

// 订单过滤条件
type OrderFilter struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            *string                `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3,oneof" json:"user_id,omitempty"`                                     // 过滤：用户 ID
	Currency          *string                `protobuf:"bytes,2,opt,name=currency,proto3,oneof" json:"currency,omitempty"`                                               // 过滤：币种
	PayStatus         *string                `protobuf:"bytes,3,opt,name=pay_status,json=payStatus,proto3,oneof" json:"pay_status,omitempty"`                            // 过滤：支付状态
	PayedMethod       *string                `protobuf:"bytes,4,opt,name=payed_method,json=payedMethod,proto3,oneof" json:"payed_method,omitempty"`                      // 过滤：支付方式
	PspProvider       *string                `protobuf:"bytes,5,opt,name=psp_provider,json=pspProvider,proto3,oneof" json:"psp_provider,omitempty"`                      // 过滤：支付服务商
	PayedAtStart      *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=payed_at_start,json=payedAtStart,proto3,oneof" json:"payed_at_start,omitempty"`                 // 过滤：支付开始时间
	PayedAtEnd        *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=payed_at_end,json=payedAtEnd,proto3,oneof" json:"payed_at_end,omitempty"`                       // 过滤：支付结束时间
	RefundStatus      *string                `protobuf:"bytes,8,opt,name=refund_status,json=refundStatus,proto3,oneof" json:"refund_status,omitempty"`                   // 过滤：退款状态
	RefundedAtStart   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=refunded_at_start,json=refundedAtStart,proto3,oneof" json:"refunded_at_start,omitempty"`        // 过滤：退款开始时间
	RefundedAtEnd     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=refunded_at_end,json=refundedAtEnd,proto3,oneof" json:"refunded_at_end,omitempty"`             // 过滤：退款结束时间
	PspPriceId        *string                `protobuf:"bytes,11,opt,name=psp_price_id,json=pspPriceId,proto3,oneof" json:"psp_price_id,omitempty"`                      // 过滤：支付平台价格 ID
	PspCustomerEmail  *string                `protobuf:"bytes,12,opt,name=psp_customer_email,json=pspCustomerEmail,proto3,oneof" json:"psp_customer_email,omitempty"`    // 过滤：支付平台客户邮箱
	PspSubscriptionId *string                `protobuf:"bytes,13,opt,name=psp_subscription_id,json=pspSubscriptionId,proto3,oneof" json:"psp_subscription_id,omitempty"` // 过滤：支付平台订阅 ID
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *OrderFilter) Reset() {
	*x = OrderFilter{}
	mi := &file_protos_order_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderFilter) ProtoMessage() {}

func (x *OrderFilter) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderFilter.ProtoReflect.Descriptor instead.
func (*OrderFilter) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{2}
}

func (x *OrderFilter) GetUserId() string {
	if x != nil && x.UserId != nil {
		return *x.UserId
	}
	return ""
}

func (x *OrderFilter) GetCurrency() string {
	if x != nil && x.Currency != nil {
		return *x.Currency
	}
	return ""
}

func (x *OrderFilter) GetPayStatus() string {
	if x != nil && x.PayStatus != nil {
		return *x.PayStatus
	}
	return ""
}

func (x *OrderFilter) GetPayedMethod() string {
	if x != nil && x.PayedMethod != nil {
		return *x.PayedMethod
	}
	return ""
}

func (x *OrderFilter) GetPspProvider() string {
	if x != nil && x.PspProvider != nil {
		return *x.PspProvider
	}
	return ""
}

func (x *OrderFilter) GetPayedAtStart() *timestamppb.Timestamp {
	if x != nil {
		return x.PayedAtStart
	}
	return nil
}

func (x *OrderFilter) GetPayedAtEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.PayedAtEnd
	}
	return nil
}

func (x *OrderFilter) GetRefundStatus() string {
	if x != nil && x.RefundStatus != nil {
		return *x.RefundStatus
	}
	return ""
}

func (x *OrderFilter) GetRefundedAtStart() *timestamppb.Timestamp {
	if x != nil {
		return x.RefundedAtStart
	}
	return nil
}

func (x *OrderFilter) GetRefundedAtEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.RefundedAtEnd
	}
	return nil
}

func (x *OrderFilter) GetPspPriceId() string {
	if x != nil && x.PspPriceId != nil {
		return *x.PspPriceId
	}
	return ""
}

func (x *OrderFilter) GetPspCustomerEmail() string {
	if x != nil && x.PspCustomerEmail != nil {
		return *x.PspCustomerEmail
	}
	return ""
}

func (x *OrderFilter) GetPspSubscriptionId() string {
	if x != nil && x.PspSubscriptionId != nil {
		return *x.PspSubscriptionId
	}
	return ""
}

// 分页请求
type PaginationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`   // 每页数量
	Offset        int32                  `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"` // 偏移量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationRequest) Reset() {
	*x = PaginationRequest{}
	mi := &file_protos_order_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationRequest) ProtoMessage() {}

func (x *PaginationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationRequest.ProtoReflect.Descriptor instead.
func (*PaginationRequest) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{3}
}

func (x *PaginationRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *PaginationRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

// 分页响应
type PaginationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`         // 总记录数
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`         // 当前页大小
	Offset        int32                  `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`       // 当前偏移量
	Remaining     int64                  `protobuf:"varint,4,opt,name=remaining,proto3" json:"remaining,omitempty"` // 剩余记录数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaginationResponse) Reset() {
	*x = PaginationResponse{}
	mi := &file_protos_order_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaginationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaginationResponse) ProtoMessage() {}

func (x *PaginationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaginationResponse.ProtoReflect.Descriptor instead.
func (*PaginationResponse) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{4}
}

func (x *PaginationResponse) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PaginationResponse) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *PaginationResponse) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *PaginationResponse) GetRemaining() int64 {
	if x != nil {
		return x.Remaining
	}
	return 0
}

// 获取所有订单请求
type ListAllOrdersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filter        *OrderFilter           `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`         // 过滤条件
	Pagination    *PaginationRequest     `protobuf:"bytes,2,opt,name=pagination,proto3,oneof" json:"pagination,omitempty"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllOrdersRequest) Reset() {
	*x = ListAllOrdersRequest{}
	mi := &file_protos_order_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllOrdersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllOrdersRequest) ProtoMessage() {}

func (x *ListAllOrdersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllOrdersRequest.ProtoReflect.Descriptor instead.
func (*ListAllOrdersRequest) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{5}
}

func (x *ListAllOrdersRequest) GetFilter() *OrderFilter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListAllOrdersRequest) GetPagination() *PaginationRequest {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 获取所有订单响应
type ListAllOrdersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Orders        []*Order               `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`         // 订单列表
	Pagination    *PaginationResponse    `protobuf:"bytes,2,opt,name=pagination,proto3" json:"pagination,omitempty"` // 分页响应信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAllOrdersResponse) Reset() {
	*x = ListAllOrdersResponse{}
	mi := &file_protos_order_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAllOrdersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAllOrdersResponse) ProtoMessage() {}

func (x *ListAllOrdersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAllOrdersResponse.ProtoReflect.Descriptor instead.
func (*ListAllOrdersResponse) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{6}
}

func (x *ListAllOrdersResponse) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *ListAllOrdersResponse) GetPagination() *PaginationResponse {
	if x != nil {
		return x.Pagination
	}
	return nil
}

// 强制退款请求
type ForceRefundRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"` // 订单ID
	Amount        *float64               `protobuf:"fixed64,2,opt,name=amount,proto3,oneof" json:"amount,omitempty"`          // 退款金额（可选，不指定则全额退款）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForceRefundRequest) Reset() {
	*x = ForceRefundRequest{}
	mi := &file_protos_order_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForceRefundRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceRefundRequest) ProtoMessage() {}

func (x *ForceRefundRequest) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceRefundRequest.ProtoReflect.Descriptor instead.
func (*ForceRefundRequest) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{7}
}

func (x *ForceRefundRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ForceRefundRequest) GetAmount() float64 {
	if x != nil && x.Amount != nil {
		return *x.Amount
	}
	return 0
}

// 强制退款响应
type ForceRefundResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // 是否成功
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`  // 响应消息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForceRefundResponse) Reset() {
	*x = ForceRefundResponse{}
	mi := &file_protos_order_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForceRefundResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceRefundResponse) ProtoMessage() {}

func (x *ForceRefundResponse) ProtoReflect() protoreflect.Message {
	mi := &file_protos_order_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceRefundResponse.ProtoReflect.Descriptor instead.
func (*ForceRefundResponse) Descriptor() ([]byte, []int) {
	return file_protos_order_proto_rawDescGZIP(), []int{8}
}

func (x *ForceRefundResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ForceRefundResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_protos_order_proto protoreflect.FileDescriptor

const file_protos_order_proto_rawDesc = "" +
	"\n" +
	"\x12protos/order.proto\x12\x17com.aibook.payment.grpc\x1a\x1fgoogle/protobuf/timestamp.proto\"m\n" +
	"\x0fProductSnapshot\x12\x1d\n" +
	"\n" +
	"user_email\x18\x01 \x01(\tR\tuserEmail\x12\x18\n" +
	"\acountry\x18\x02 \x01(\tR\acountry\x12!\n" +
	"\fpackage_type\x18\x03 \x01(\tR\vpackageType\"\xc7\t\n" +
	"\x05Order\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x19\n" +
	"\border_id\x18\x02 \x01(\tR\aorderId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x1d\n" +
	"\n" +
	"product_id\x18\x04 \x01(\tR\tproductId\x12!\n" +
	"\fproduct_desc\x18\x05 \x01(\tR\vproductDesc\x12)\n" +
	"\x10product_snapshot\x18\x06 \x01(\tR\x0fproductSnapshot\x12\x19\n" +
	"\bprice_id\x18\a \x01(\tR\apriceId\x12\x1a\n" +
	"\bquantity\x18\b \x01(\rR\bquantity\x12\x16\n" +
	"\x06amount\x18\t \x01(\x01R\x06amount\x12\x1d\n" +
	"\n" +
	"net_amount\x18\n" +
	" \x01(\x01R\tnetAmount\x12\x1a\n" +
	"\bcurrency\x18\v \x01(\tR\bcurrency\x12\x1d\n" +
	"\n" +
	"pay_status\x18\f \x01(\tR\tpayStatus\x12\x17\n" +
	"\apay_ret\x18\r \x01(\tR\x06payRet\x12!\n" +
	"\fpayed_method\x18\x0e \x01(\tR\vpayedMethod\x12!\n" +
	"\fpsp_provider\x18\x0f \x01(\tR\vpspProvider\x12\x1f\n" +
	"\vcard_number\x18\x10 \x01(\tR\n" +
	"cardNumber\x12:\n" +
	"\bpayed_at\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampH\x00R\apayedAt\x88\x01\x01\x12#\n" +
	"\rrefund_status\x18\x12 \x01(\tR\frefundStatus\x12@\n" +
	"\vrefunded_at\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"refundedAt\x88\x01\x01\x12$\n" +
	"\x0epsp_payment_id\x18\x14 \x01(\tR\fpspPaymentId\x121\n" +
	"\x15psp_payment_intent_id\x18\x15 \x01(\tR\x12pspPaymentIntentId\x121\n" +
	"\x15psp_payment_refund_id\x18\x16 \x01(\tR\x12pspPaymentRefundId\x123\n" +
	"\x16psp_payment_refund_ret\x18\x17 \x01(\tR\x13pspPaymentRefundRet\x12&\n" +
	"\x0fpsp_customer_id\x18\x18 \x01(\tR\rpspCustomerId\x12,\n" +
	"\x12psp_customer_email\x18\x19 \x01(\tR\x10pspCustomerEmail\x12.\n" +
	"\x13psp_subscription_id\x18\x1a \x01(\tR\x11pspSubscriptionId\x129\n" +
	"\n" +
	"created_at\x18\x1b \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x1c \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x18\n" +
	"\adeleted\x18\x1d \x01(\bR\adeleted\x12>\n" +
	"\n" +
	"deleted_at\x18\x1e \x01(\v2\x1a.google.protobuf.TimestampH\x02R\tdeletedAt\x88\x01\x01B\v\n" +
	"\t_payed_atB\x0e\n" +
	"\f_refunded_atB\r\n" +
	"\v_deleted_at\"\x83\a\n" +
	"\vOrderFilter\x12\x1c\n" +
	"\auser_id\x18\x01 \x01(\tH\x00R\x06userId\x88\x01\x01\x12\x1f\n" +
	"\bcurrency\x18\x02 \x01(\tH\x01R\bcurrency\x88\x01\x01\x12\"\n" +
	"\n" +
	"pay_status\x18\x03 \x01(\tH\x02R\tpayStatus\x88\x01\x01\x12&\n" +
	"\fpayed_method\x18\x04 \x01(\tH\x03R\vpayedMethod\x88\x01\x01\x12&\n" +
	"\fpsp_provider\x18\x05 \x01(\tH\x04R\vpspProvider\x88\x01\x01\x12E\n" +
	"\x0epayed_at_start\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampH\x05R\fpayedAtStart\x88\x01\x01\x12A\n" +
	"\fpayed_at_end\x18\a \x01(\v2\x1a.google.protobuf.TimestampH\x06R\n" +
	"payedAtEnd\x88\x01\x01\x12(\n" +
	"\rrefund_status\x18\b \x01(\tH\aR\frefundStatus\x88\x01\x01\x12K\n" +
	"\x11refunded_at_start\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\bR\x0frefundedAtStart\x88\x01\x01\x12G\n" +
	"\x0frefunded_at_end\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampH\tR\rrefundedAtEnd\x88\x01\x01\x12%\n" +
	"\fpsp_price_id\x18\v \x01(\tH\n" +
	"R\n" +
	"pspPriceId\x88\x01\x01\x121\n" +
	"\x12psp_customer_email\x18\f \x01(\tH\vR\x10pspCustomerEmail\x88\x01\x01\x123\n" +
	"\x13psp_subscription_id\x18\r \x01(\tH\fR\x11pspSubscriptionId\x88\x01\x01B\n" +
	"\n" +
	"\b_user_idB\v\n" +
	"\t_currencyB\r\n" +
	"\v_pay_statusB\x0f\n" +
	"\r_payed_methodB\x0f\n" +
	"\r_psp_providerB\x11\n" +
	"\x0f_payed_at_startB\x0f\n" +
	"\r_payed_at_endB\x10\n" +
	"\x0e_refund_statusB\x14\n" +
	"\x12_refunded_at_startB\x12\n" +
	"\x10_refunded_at_endB\x0f\n" +
	"\r_psp_price_idB\x15\n" +
	"\x13_psp_customer_emailB\x16\n" +
	"\x14_psp_subscription_id\"A\n" +
	"\x11PaginationRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x02 \x01(\x05R\x06offset\"v\n" +
	"\x12PaginationResponse\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\x12\x16\n" +
	"\x06offset\x18\x03 \x01(\x05R\x06offset\x12\x1c\n" +
	"\tremaining\x18\x04 \x01(\x03R\tremaining\"\xc4\x01\n" +
	"\x14ListAllOrdersRequest\x12A\n" +
	"\x06filter\x18\x01 \x01(\v2$.com.aibook.payment.grpc.OrderFilterH\x00R\x06filter\x88\x01\x01\x12O\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2*.com.aibook.payment.grpc.PaginationRequestH\x01R\n" +
	"pagination\x88\x01\x01B\t\n" +
	"\a_filterB\r\n" +
	"\v_pagination\"\x9c\x01\n" +
	"\x15ListAllOrdersResponse\x126\n" +
	"\x06orders\x18\x01 \x03(\v2\x1e.com.aibook.payment.grpc.OrderR\x06orders\x12K\n" +
	"\n" +
	"pagination\x18\x02 \x01(\v2+.com.aibook.payment.grpc.PaginationResponseR\n" +
	"pagination\"W\n" +
	"\x12ForceRefundRequest\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x12\x1b\n" +
	"\x06amount\x18\x02 \x01(\x01H\x00R\x06amount\x88\x01\x01B\t\n" +
	"\a_amount\"I\n" +
	"\x13ForceRefundResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage2\xe8\x01\n" +
	"\fOrderService\x12n\n" +
	"\rListAllOrders\x12-.com.aibook.payment.grpc.ListAllOrdersRequest\x1a..com.aibook.payment.grpc.ListAllOrdersResponse\x12h\n" +
	"\vForceRefund\x12+.com.aibook.payment.grpc.ForceRefundRequest\x1a,.com.aibook.payment.grpc.ForceRefundResponseB\x19P\x01Z\x15./paymentpb;paymentpbb\x06proto3"

var (
	file_protos_order_proto_rawDescOnce sync.Once
	file_protos_order_proto_rawDescData []byte
)

func file_protos_order_proto_rawDescGZIP() []byte {
	file_protos_order_proto_rawDescOnce.Do(func() {
		file_protos_order_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_protos_order_proto_rawDesc), len(file_protos_order_proto_rawDesc)))
	})
	return file_protos_order_proto_rawDescData
}

var file_protos_order_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_protos_order_proto_goTypes = []any{
	(*ProductSnapshot)(nil),       // 0: com.aibook.payment.grpc.ProductSnapshot
	(*Order)(nil),                 // 1: com.aibook.payment.grpc.Order
	(*OrderFilter)(nil),           // 2: com.aibook.payment.grpc.OrderFilter
	(*PaginationRequest)(nil),     // 3: com.aibook.payment.grpc.PaginationRequest
	(*PaginationResponse)(nil),    // 4: com.aibook.payment.grpc.PaginationResponse
	(*ListAllOrdersRequest)(nil),  // 5: com.aibook.payment.grpc.ListAllOrdersRequest
	(*ListAllOrdersResponse)(nil), // 6: com.aibook.payment.grpc.ListAllOrdersResponse
	(*ForceRefundRequest)(nil),    // 7: com.aibook.payment.grpc.ForceRefundRequest
	(*ForceRefundResponse)(nil),   // 8: com.aibook.payment.grpc.ForceRefundResponse
	(*timestamppb.Timestamp)(nil), // 9: google.protobuf.Timestamp
}
var file_protos_order_proto_depIdxs = []int32{
	9,  // 0: com.aibook.payment.grpc.Order.payed_at:type_name -> google.protobuf.Timestamp
	9,  // 1: com.aibook.payment.grpc.Order.refunded_at:type_name -> google.protobuf.Timestamp
	9,  // 2: com.aibook.payment.grpc.Order.created_at:type_name -> google.protobuf.Timestamp
	9,  // 3: com.aibook.payment.grpc.Order.updated_at:type_name -> google.protobuf.Timestamp
	9,  // 4: com.aibook.payment.grpc.Order.deleted_at:type_name -> google.protobuf.Timestamp
	9,  // 5: com.aibook.payment.grpc.OrderFilter.payed_at_start:type_name -> google.protobuf.Timestamp
	9,  // 6: com.aibook.payment.grpc.OrderFilter.payed_at_end:type_name -> google.protobuf.Timestamp
	9,  // 7: com.aibook.payment.grpc.OrderFilter.refunded_at_start:type_name -> google.protobuf.Timestamp
	9,  // 8: com.aibook.payment.grpc.OrderFilter.refunded_at_end:type_name -> google.protobuf.Timestamp
	2,  // 9: com.aibook.payment.grpc.ListAllOrdersRequest.filter:type_name -> com.aibook.payment.grpc.OrderFilter
	3,  // 10: com.aibook.payment.grpc.ListAllOrdersRequest.pagination:type_name -> com.aibook.payment.grpc.PaginationRequest
	1,  // 11: com.aibook.payment.grpc.ListAllOrdersResponse.orders:type_name -> com.aibook.payment.grpc.Order
	4,  // 12: com.aibook.payment.grpc.ListAllOrdersResponse.pagination:type_name -> com.aibook.payment.grpc.PaginationResponse
	5,  // 13: com.aibook.payment.grpc.OrderService.ListAllOrders:input_type -> com.aibook.payment.grpc.ListAllOrdersRequest
	7,  // 14: com.aibook.payment.grpc.OrderService.ForceRefund:input_type -> com.aibook.payment.grpc.ForceRefundRequest
	6,  // 15: com.aibook.payment.grpc.OrderService.ListAllOrders:output_type -> com.aibook.payment.grpc.ListAllOrdersResponse
	8,  // 16: com.aibook.payment.grpc.OrderService.ForceRefund:output_type -> com.aibook.payment.grpc.ForceRefundResponse
	15, // [15:17] is the sub-list for method output_type
	13, // [13:15] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_protos_order_proto_init() }
func file_protos_order_proto_init() {
	if File_protos_order_proto != nil {
		return
	}
	file_protos_order_proto_msgTypes[1].OneofWrappers = []any{}
	file_protos_order_proto_msgTypes[2].OneofWrappers = []any{}
	file_protos_order_proto_msgTypes[5].OneofWrappers = []any{}
	file_protos_order_proto_msgTypes[7].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_protos_order_proto_rawDesc), len(file_protos_order_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_protos_order_proto_goTypes,
		DependencyIndexes: file_protos_order_proto_depIdxs,
		MessageInfos:      file_protos_order_proto_msgTypes,
	}.Build()
	File_protos_order_proto = out.File
	file_protos_order_proto_goTypes = nil
	file_protos_order_proto_depIdxs = nil
}
