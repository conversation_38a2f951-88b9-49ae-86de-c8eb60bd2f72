package main

import (
	"encoding/json"
	"fmt"
	"io"
	"time"
)

// testAdminAddPackagesRPC 测试内网创建流量包接口 (Dubbo RPC)
func testAdminAddPackagesRPC(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Add Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.storepb.grpc.StoreService/AdminAddPackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := CreatePackageRequest{
		PackageName:     "RPC测试流量包",
		PackageDesc:     "用于RPC API测试的流量包",
		PackageType:     "traffic",
		Entitlement:     200,
		EntitlementDesc: "200GB流量",
		OriginalPrice:   29.99,
		DiscountPrice:   nil,
		DiscountDesc:    "",
		SaleStatus:      "on_sale",
		Currency:        "USD",
		Country:         "US",
		Extra1:          "test",
		Extra2:          "rpc",
		Extra3:          100,
		Extra4:          30,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {

		// 从响应中解析出创建的流量包ID
		packageID := ""
		var packageResp CreatePackageResponse
		if err := json.Unmarshal(body, &packageResp); err == nil {
			packageID = packageResp.PackageID
		}

		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package created successfully via Dubbo RPC",
			Duration: duration,
			CtxMapString: map[string]string{"rpc_package_id_to_update": packageID,
				"rpc_package_id_to_buy": packageID},
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminListAllPackagesRPC 测试内网获取所有流量包接口 (Dubbo RPC)
func testAdminListAllPackagesRPC(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin List All Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.storepb.grpc.StoreService/AdminListAllPackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := map[string]interface{}{
		"pagination": map[string]interface{}{
			"limit":  50,
			"offset": 0,
		},
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Admin packages retrieved successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminUpdatePackagesRPC 测试内网更新流量包接口 (Dubbo RPC)
func testAdminUpdatePackagesRPC(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Update Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.storepb.grpc.StoreService/AdminUpdatePackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	// 构造更新流量包请求体 - 需要先有一个存在的package_id
	res, rpc_package_id_to_update, err := GetCtxValue("rpc_package_id_to_update", results, testName, start)
	if err != nil {
		return *res
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	newPrice := 124.99
	packageName := "RPC更新后的测试流量包"
	originalPrice := newPrice
	requestBody := UpdatePackageRequest{
		PackageID:     rpc_package_id_to_update,
		PackageName:   &packageName,
		OriginalPrice: &originalPrice,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		return TestResult{
			TestName:     testName,
			Success:      true,
			Message:      "Package updated successfully via Dubbo RPC",
			Duration:     duration,
			CtxMapString: map[string]string{"rpc_package_id_to_delete": rpc_package_id_to_update},
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}

// testAdminDeletePackagesRPC 测试内网删除流量包接口 (Dubbo RPC)
func testAdminDeletePackagesRPC(config *EnvConfig, results []TestResult) TestResult {
	start := time.Now()
	testName := "Admin Delete Packages (Internal Dubbo RPC)"

	// 使用 HTTP 协议调用 Dubbo RPC 接口
	url := config.LocalRpcURL + "/com.aibook.storepb.grpc.StoreService/AdminDeletePackages"
	headers := map[string]string{
		"x-trace-id": generateTraceID(),
	}

	preCount, err := getAdminAllPackagesCountGin(config)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to get package count: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to get package count: %v", err),
			Duration: time.Since(start),
		}
	}

	res, rpc_package_id_to_delete, err := GetCtxValue("rpc_package_id_to_delete", results, testName, start)
	if err != nil {
		return *res
	}

	// 构造 RPC 请求体 - 根据 protobuf 定义
	requestBody := DeletePackageRequest{
		PackageID: rpc_package_id_to_delete,
	}

	// 记录详细请求信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("--- %s ---", testName)
		detailLogger.Printf("URL: %s", url)
		detailLogger.Printf("Headers: %+v", headers)
		detailLogger.Printf("Request Body: %+v", requestBody)
	}

	resp, err := makeHTTPRequest("POST", url, headers, requestBody)
	duration := time.Since(start)

	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Request failed: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Request failed: %v", err),
			Duration: duration,
		}
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		if detailLogger != nil {
			detailLogger.Printf("Failed to read response: %v", err)
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("Failed to read response: %v", err),
			Duration: duration,
		}
	}

	// 记录详细响应信息到日志文件
	if detailLogger != nil {
		detailLogger.Printf("Response Status: %d", resp.StatusCode)
		detailLogger.Printf("Response Body: %s", prettyJson(body))
	}

	// 简要信息输出到控制台
	fmt.Printf("  %s: Status=%d\n", testName, resp.StatusCode)
	if resp.StatusCode == 200 {
		fmt.Printf("  %s: Package deleted successfully\n", testName)

		afterCount, err := getAdminAllPackagesCountGin(config)
		if err != nil {
			if detailLogger != nil {
				detailLogger.Printf("Failed to get package count: %v", err)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  fmt.Sprintf("Failed to get package count: %v", err),
				Duration: time.Since(start),
			}
		}

		fmt.Printf("  %s: afterCount:%v, preCount:%v\n", testName, afterCount, preCount)

		if afterCount != preCount-1 {
			if detailLogger != nil {
				detailLogger.Printf("Package count not match, preCount=%d, afterCount=%d", preCount, afterCount)
			}
			return TestResult{
				TestName: testName,
				Success:  false,
				Message:  fmt.Sprintf("Package count not match, preCount=%d, afterCount=%d", preCount, afterCount),
				Duration: time.Since(start),
			}
		}

		return TestResult{
			TestName: testName,
			Success:  true,
			Message:  "Package deleted successfully via Dubbo RPC",
			Duration: duration,
		}
	} else {
		if detailLogger != nil {
			detailLogger.Printf("Request failed with HTTP %d: %s", resp.StatusCode, string(body))
		}
		return TestResult{
			TestName: testName,
			Success:  false,
			Message:  fmt.Sprintf("HTTP %d: %s", resp.StatusCode, string(body)),
			Duration: duration,
		}
	}
}
