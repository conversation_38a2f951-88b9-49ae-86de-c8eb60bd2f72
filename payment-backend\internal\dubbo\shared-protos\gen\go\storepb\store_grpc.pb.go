// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.21.12
// source: protos/store.proto

package storepb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	StoreService_ListAllPackages_FullMethodName      = "/com.aibook.storepb.grpc.StoreService/ListAllPackages"
	StoreService_AdminAddPackages_FullMethodName     = "/com.aibook.storepb.grpc.StoreService/AdminAddPackages"
	StoreService_AdminDeletePackages_FullMethodName  = "/com.aibook.storepb.grpc.StoreService/AdminDeletePackages"
	StoreService_AdminUpdatePackages_FullMethodName  = "/com.aibook.storepb.grpc.StoreService/AdminUpdatePackages"
	StoreService_AdminListAllPackages_FullMethodName = "/com.aibook.storepb.grpc.StoreService/AdminListAllPackages"
)

// StoreServiceClient is the client API for StoreService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 流量包服务定义
type StoreServiceClient interface {
	// 获取所有流量包列表（终端用户接口）
	ListAllPackages(ctx context.Context, in *ListAllPackagesRequest, opts ...grpc.CallOption) (*ListAllPackagesResponse, error)
	// 添加流量包（管理员接口）
	AdminAddPackages(ctx context.Context, in *CreatePackageRequest, opts ...grpc.CallOption) (*AdminPackageResponse, error)
	// 删除流量包（管理员接口）
	AdminDeletePackages(ctx context.Context, in *DeletePackageRequest, opts ...grpc.CallOption) (*DeletePackageResponse, error)
	// 更新流量包（管理员接口）
	AdminUpdatePackages(ctx context.Context, in *UpdatePackageRequest, opts ...grpc.CallOption) (*UpdatePackageResponse, error)
	// 获取所有流量包列表（管理员接口）
	AdminListAllPackages(ctx context.Context, in *AdminListAllPackagesRequest, opts ...grpc.CallOption) (*AdminListAllPackagesResponse, error)
}

type storeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStoreServiceClient(cc grpc.ClientConnInterface) StoreServiceClient {
	return &storeServiceClient{cc}
}

func (c *storeServiceClient) ListAllPackages(ctx context.Context, in *ListAllPackagesRequest, opts ...grpc.CallOption) (*ListAllPackagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAllPackagesResponse)
	err := c.cc.Invoke(ctx, StoreService_ListAllPackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) AdminAddPackages(ctx context.Context, in *CreatePackageRequest, opts ...grpc.CallOption) (*AdminPackageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminPackageResponse)
	err := c.cc.Invoke(ctx, StoreService_AdminAddPackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) AdminDeletePackages(ctx context.Context, in *DeletePackageRequest, opts ...grpc.CallOption) (*DeletePackageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePackageResponse)
	err := c.cc.Invoke(ctx, StoreService_AdminDeletePackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) AdminUpdatePackages(ctx context.Context, in *UpdatePackageRequest, opts ...grpc.CallOption) (*UpdatePackageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePackageResponse)
	err := c.cc.Invoke(ctx, StoreService_AdminUpdatePackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *storeServiceClient) AdminListAllPackages(ctx context.Context, in *AdminListAllPackagesRequest, opts ...grpc.CallOption) (*AdminListAllPackagesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AdminListAllPackagesResponse)
	err := c.cc.Invoke(ctx, StoreService_AdminListAllPackages_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StoreServiceServer is the server API for StoreService service.
// All implementations must embed UnimplementedStoreServiceServer
// for forward compatibility.
//
// 流量包服务定义
type StoreServiceServer interface {
	// 获取所有流量包列表（终端用户接口）
	ListAllPackages(context.Context, *ListAllPackagesRequest) (*ListAllPackagesResponse, error)
	// 添加流量包（管理员接口）
	AdminAddPackages(context.Context, *CreatePackageRequest) (*AdminPackageResponse, error)
	// 删除流量包（管理员接口）
	AdminDeletePackages(context.Context, *DeletePackageRequest) (*DeletePackageResponse, error)
	// 更新流量包（管理员接口）
	AdminUpdatePackages(context.Context, *UpdatePackageRequest) (*UpdatePackageResponse, error)
	// 获取所有流量包列表（管理员接口）
	AdminListAllPackages(context.Context, *AdminListAllPackagesRequest) (*AdminListAllPackagesResponse, error)
	mustEmbedUnimplementedStoreServiceServer()
}

// UnimplementedStoreServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStoreServiceServer struct{}

func (UnimplementedStoreServiceServer) ListAllPackages(context.Context, *ListAllPackagesRequest) (*ListAllPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAllPackages not implemented")
}
func (UnimplementedStoreServiceServer) AdminAddPackages(context.Context, *CreatePackageRequest) (*AdminPackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminAddPackages not implemented")
}
func (UnimplementedStoreServiceServer) AdminDeletePackages(context.Context, *DeletePackageRequest) (*DeletePackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminDeletePackages not implemented")
}
func (UnimplementedStoreServiceServer) AdminUpdatePackages(context.Context, *UpdatePackageRequest) (*UpdatePackageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminUpdatePackages not implemented")
}
func (UnimplementedStoreServiceServer) AdminListAllPackages(context.Context, *AdminListAllPackagesRequest) (*AdminListAllPackagesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AdminListAllPackages not implemented")
}
func (UnimplementedStoreServiceServer) mustEmbedUnimplementedStoreServiceServer() {}
func (UnimplementedStoreServiceServer) testEmbeddedByValue()                      {}

// UnsafeStoreServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StoreServiceServer will
// result in compilation errors.
type UnsafeStoreServiceServer interface {
	mustEmbedUnimplementedStoreServiceServer()
}

func RegisterStoreServiceServer(s grpc.ServiceRegistrar, srv StoreServiceServer) {
	// If the following call pancis, it indicates UnimplementedStoreServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StoreService_ServiceDesc, srv)
}

func _StoreService_ListAllPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAllPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).ListAllPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_ListAllPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).ListAllPackages(ctx, req.(*ListAllPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_AdminAddPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).AdminAddPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_AdminAddPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).AdminAddPackages(ctx, req.(*CreatePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_AdminDeletePackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).AdminDeletePackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_AdminDeletePackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).AdminDeletePackages(ctx, req.(*DeletePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_AdminUpdatePackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePackageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).AdminUpdatePackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_AdminUpdatePackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).AdminUpdatePackages(ctx, req.(*UpdatePackageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StoreService_AdminListAllPackages_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AdminListAllPackagesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StoreServiceServer).AdminListAllPackages(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StoreService_AdminListAllPackages_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StoreServiceServer).AdminListAllPackages(ctx, req.(*AdminListAllPackagesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StoreService_ServiceDesc is the grpc.ServiceDesc for StoreService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StoreService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "com.aibook.storepb.grpc.StoreService",
	HandlerType: (*StoreServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListAllPackages",
			Handler:    _StoreService_ListAllPackages_Handler,
		},
		{
			MethodName: "AdminAddPackages",
			Handler:    _StoreService_AdminAddPackages_Handler,
		},
		{
			MethodName: "AdminDeletePackages",
			Handler:    _StoreService_AdminDeletePackages_Handler,
		},
		{
			MethodName: "AdminUpdatePackages",
			Handler:    _StoreService_AdminUpdatePackages_Handler,
		},
		{
			MethodName: "AdminListAllPackages",
			Handler:    _StoreService_AdminListAllPackages_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "protos/store.proto",
}
