package main

import (
	"fmt"
	"io"
	"log"
	"os"
	"strings"
	"time"
)

var (
	// 详细日志记录器，只输出到文件
	detailLogger *log.Logger
	// 汇总日志记录器，同时输出到控制台和文件
	summaryLogger *log.Logger
)

func main() {
	// 创建日志文件
	logFile, err := os.OpenFile("test.log", os.O_CREATE|os.O_RDWR|os.O_TRUNC, 0666)
	if err != nil {
		log.Fatalf("Failed to create log file: %v", err)
	}
	defer logFile.Close()

	// 设置详细日志输出到文件
	detailLogger = log.New(logFile, "", log.LstdFlags)

	// 设置汇总日志同时输出到控制台和文件
	summaryLogger = log.New(io.MultiWriter(os.Stdout, logFile), "", 0)

	run_env := "all"
	if len(os.Args) > 1 {
		run_env = os.Args[1]
	}
	if run_env == "all" {
		run_env = "dev1,dev2,sit"
	}

	run_envs := make([]string, 0)
	for _, env := range strings.Split(run_env, ",") {
		run_envs = append(run_envs, strings.TrimSpace(env))
	}

	// 记录测试开始时间到日志文件
	detailLogger.Printf("=== Test Session Started at %v ===", time.Now().Format("2006-01-02 15:04:05"))

	testResults := make([][]TestResult, 0)
	testedRunEnvs := make([]string, 0)
	for _, run_env := range run_envs {
		detailLogger.Printf("+++++++++++++++ Starting test for %v +++++++++++++++", run_env)
		fmt.Printf("+++++++++++++++ Starting test for %v +++++++++++++++\n", run_env)
		testResult := StartTest(run_env)
		if testResult != nil {
			testResults = append(testResults, testResult)
			testedRunEnvs = append(testedRunEnvs, run_env)
		}
		detailLogger.Printf("--------------- Finished test for %v ---------------", run_env)
		fmt.Printf("--------------- Finished test for %v ---------------\n\n", run_env)
	}

	// 记录汇总开始到日志文件
	detailLogger.Printf("=== Starting Test Summary ===")

	summaryLogger.Printf("\n\n##################### EACH TEST SUMMARY #####################")
	for i, testResult := range testResults {
		printTestSummary(testedRunEnvs[i], testResult)
		summaryLogger.Println()
	}

	// 记录测试结束时间到日志文件
	detailLogger.Printf("=== Test Session Ended at %v ===", time.Now().Format("2006-01-02 15:04:05"))
}

func StartTest(run_env string) []TestResult {
	config := getEnvConfig(run_env)
	if config == nil {
		msg := fmt.Sprintf("Unknown environment: %s", run_env)
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("%s", msg)
		return nil
	}

	// 详细信息记录到日志文件
	detailLogger.Printf("Testing environment: %s", config.Name)
	detailLogger.Printf("External URL: %s", config.ExternalURL)
	detailLogger.Printf("Local Gin URL: %s", config.LocalGinURL)
	detailLogger.Printf("Local RPC URL: %s", config.LocalRpcURL)
	detailLogger.Printf("")

	// 简要信息输出到控制台
	fmt.Printf("Testing environment: %s\n", config.Name)
	fmt.Printf("External URL: %s\n", config.ExternalURL)
	fmt.Printf("Local Gin URL: %s\n", config.LocalGinURL)
	fmt.Printf("Local RPC URL: %s\n", config.LocalRpcURL)
	fmt.Println()

	var results []TestResult

	// 获取外网接口的token
	detailLogger.Printf("=== Getting External API Token ===")
	fmt.Println("=== Getting External API Token ===")
	token, err := getExternalToken(config)
	if err != nil {
		msg := fmt.Sprintf("Failed to get external token: %v", err)
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("%s", msg)
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  false,
			Message:  fmt.Sprintf("Failed to get token: %v", err),
			Duration: 0,
		})
	} else {
		msg := fmt.Sprintf("Token obtained successfully: %s...", token[:50])
		fmt.Printf("%s\n", msg)
		detailLogger.Printf("Token obtained successfully: %s", token)
		results = append(results, TestResult{
			TestName: "Get External Token",
			Success:  true,
			Message:  "Token obtained successfully",
			Duration: 0,
		})
	}

	// 流量包增查改 (Gin HTTP)
	// results = append(results, testAdminAddPackagesGin(config, results)) // [暂不用测试] 暂时只测试 RPC， 防止混乱.
	// results = append(results, testAdminListAllPackagesGin(config, results)) //  [暂不用测试] 暂时只测试 RPC， 防止混乱.
	// results = append(results, testAdminUpdatePackagesGin(config, results)) //  [暂不用测试] 暂时只测试 RPC， 防止混乱.
	// 流量包增查改 (Dubbo RPC)
	results = append(results, testAdminAddPackagesRPC(config, results))
	results = append(results, testAdminListAllPackagesRPC(config, results))
	results = append(results, testAdminUpdatePackagesRPC(config, results))
	// 流量包查[外网] (Dubbo RPC)
	results = append(results, testListAllPackagesExternal(config, results, token))
	results = append(results, testListAllPackagesExternalWithFilters(config, results, token))

	// 创建订单[外网] (只测试 gin HTTP)
	results = append(results, testCreateOrderExternal(config, results, token))

	// 这里等待用户手动付款后再继续测试
	fmt.Println("@@@@@@ Please complete the payment manually and then press Y to continue, N to abort...")
	var input string
	fmt.Scanln(&input)
	if strings.ToLower(input) != "y" {
		fmt.Println("Aborting...")
		return results
	}
	fmt.Println("Continuing...")

	// 订单查询[外网] (Gin HTTP)
	results = append(results, testGetUserOrdersExternal(config, results, token))
	results = append(results, testListAllOrdersGin(config, results))
	// 订单查询 (Dubbo RPC)
	results = append(results, testListAllOrdersRPC(config, results))

	// 订单退款 (Dubbo RPC)
	results = append(results, testForceRefundRPC(config, results))
	// 订单退款 (Gin HTTP)
	// results = append(results, testForceRefundGin(config, results))  // [暂不用测试]目前只付款一次, 所以在 RPC 测试中已退一次款.

	// 流量包删测试 (Gin HTTP)
	// results = append(results, testAdminDeletePackagesGin(config, results)) // [暂不用测试] 目前在 RPC 测试删除.
	// 流量包删测试 (Dubbo RPC)
	results = append(results, testAdminDeletePackagesRPC(config, results))

	return results
}
