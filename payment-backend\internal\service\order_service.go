package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"payment-backend/internal/config"
	"payment-backend/internal/db"
	"payment-backend/internal/domain"
	"payment-backend/internal/domain/store"
	"payment-backend/internal/logger"
	"payment-backend/internal/middleware"

	"payment-sdk/payment"
)

// orderService 订单服务实现
type orderService struct {
	pointService   PointService
	packageService store.PackageService
	orderRepo      domain.OrderRepository
	gateways       map[string]payment.PaymentGateway
	config         *config.Config
	logger         logger.Logger
}

// NewOrderService 创建订单服务
func NewOrderService(
	pointService PointService,
	packageService store.PackageService,
	orderRepo domain.OrderRepository,
	gateways map[string]payment.PaymentGateway,
	config *config.Config,
	logger logger.Logger,
) domain.OrderService {
	return &orderService{
		pointService:   pointService,
		packageService: packageService,
		orderRepo:      orderRepo,
		gateways:       gateways,
		config:         config,
		logger:         logger,
	}
}

// CreateOrder 创建订单
func (s *orderService) CreateOrder(userCtx *middleware.UserContext,
	requestContext *middleware.RequestContext,
	req *domain.CreateOrderRequest) (*domain.CreateOrderResponse, error) {
	s.logger.Info("CreateOrder",
		logger.Object("userCtx", userCtx),
		logger.Object("s.packageService", s.packageService),
		logger.Object("req", req),
		logger.Object("requestContext", requestContext))

	var rsp *payment.CreateCheckoutRsp
	var order *domain.Order

	if len(req.PSPProvider) == 0 {
		req.PSPProvider = "stripe"
	}

	userEmail := "" // TODO: 待获取

	err := db.WithTransaction(func(tx *gorm.DB) error {
		s.logger.Debug("CreateOrder",
			logger.Object("userCtx", userCtx),
			logger.Object("s.packageService", s.packageService),
			logger.Object("req", req),
			logger.Object("requestContext", requestContext),
			logger.Object("userEmail", userEmail))

		pkgRsp, err := s.packageService.GetPackageDetail(req.ProductID)
		if err != nil {
			s.logger.Warn("CreateOrder",
				logger.String("packageService.GetPackageDetail error", err.Error()))
			return err
		}
		s.logger.Debug("CreateOrder", logger.Object("packageService.GetPackageDetail rsp", pkgRsp))

		// 在事务中创建订单
		order = domain.NewOrder(userCtx.UserID, requestContext.TraceId, req, "")
		if err := s.orderRepo.CreateWithTransaction(tx, order); err != nil {
			return err
		}

		productSnapshot := domain.ProductSnapshot{
			UserEmail:   userEmail,
			Country:     userCtx.Country,
			PackageType: pkgRsp.PackageType,
		}
		snapshot, err := json.Marshal(productSnapshot)
		if err != nil {
			s.logger.Warn("CreateOrder",
				logger.String("json.Marshal productSnapshot error", err.Error()))
			return err
		}

		order.ProductID = pkgRsp.PackageID
		order.ProductDesc = pkgRsp.PackageDesc
		order.ProductSnapshot = base64.StdEncoding.EncodeToString(snapshot)
		order.PriceID = pkgRsp.PSPPriceID
		order.Quantity = req.Quantity
		order.Amount = float64(req.Quantity) * pkgRsp.Price
		order.Currency = req.Currency

		s.logger.Debug("order created", logger.Object("order", order))

		// 创建支付会话（如果失败会回滚订单创建）
		gateway, exists := s.gateways[req.PSPProvider]
		if !exists {
			s.logger.Warn("CreateOrder",
				logger.String("payment gateway not supported", req.PSPProvider))
			return fmt.Errorf("payment gateway [%s] not supported", req.PSPProvider)
		}

		// 从配置中获取 SuccessURL 和 CancelURL
		providerConfig, exists := s.config.Payment.Providers[req.PSPProvider]
		if !exists {
			s.logger.Warn("CreateOrder",
				logger.String("payment provider not configured", req.PSPProvider))
			return fmt.Errorf("payment provider [%s] unsupported", req.PSPProvider)
		}

		// providerConfig.SuccessURL, providerConfig.CancelURL
		req := &payment.CheckoutReq{
			OrderID:     order.OrderID,
			UserID:      order.UserID,
			TraceID:     order.TraceID,
			ProductID:   order.ProductID,
			PriceID:     order.PriceID,
			Quantity:    order.Quantity,
			Country:     userCtx.Country,
			Entitlement: fmt.Sprintf("%v", pkgRsp.Entitlement),
			SuccessURL:  providerConfig.SuccessURL,
			CancelURL:   providerConfig.CancelURL,
		}
		rsp, err = gateway.CreateCheckout(req)
		if err != nil {
			s.logger.Warn("CreateOrder",
				logger.String("gateway.CreateCheckout error", err.Error()))
			return err
		}

		if err := s.orderRepo.UpdatePaymentIDWithTransaction(tx, order.OrderID, rsp.PaymentID); err != nil {
			s.logger.Warn("CreateOrder",
				logger.String("s.orderRepo.UpdatePaymentIDWithTransaction error", err.Error()))
			return err
		}
		s.logger.Debug("payment_id updated", logger.String("payment_id", rsp.PaymentID))

		return nil
	})

	if err != nil {
		return nil, err
	}
	r := &domain.CreateOrderResponse{
		OrderID:     order.OrderID,
		CheckoutURL: rsp.CheckoutURL,
		Amount:      order.Amount,
		Currency:    order.Currency,
		ExpiresAt:   time.Now().Add(24 * time.Hour), // 24小时后过期
	}

	s.logger.Info("CreateOrder", logger.Object("rsp", r))

	return r, nil
}

// GetOrder 根据订单ID获取订单
func (s *orderService) GetOrder(orderID string) (*domain.Order, error) {
	order, err := s.orderRepo.GetByOrderID(orderID)
	if err != nil {
		s.logger.Error("Failed to get order", zap.String("order_id", orderID), zap.Error(err))
		return nil, err
	}

	return order, nil
}

// GetOrderByID 根据数据库ID获取订单
func (s *orderService) GetOrderByID(id uint64) (*domain.Order, error) {
	order, err := s.orderRepo.GetByID(id)
	if err != nil {
		s.logger.Error("Failed to get order by ID", zap.Uint64("id", id), zap.Error(err))
		return nil, err
	}

	return order, nil
}

// GetUserOrders 获取用户的订单列表
func (s *orderService) GetUserOrders(userCtx *middleware.UserContext, filter *domain.OrderFilter, pagination *domain.PaginationRequest) (*domain.GetUserOrdersResponse, error) {
	s.logger.Info("orderService::GetUserOrders",
		zap.Any("filter", filter),
		zap.Any("pagination", pagination))

	// 调用仓储层获取数据
	orders, total, err := s.orderRepo.GetByUserID(userCtx.UserID, filter, pagination)
	if err != nil {
		s.logger.Error("Failed to list orders with filters",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		return nil, err
	}

	// 计算剩余记录数
	remaining := total - int64(pagination.Offset+len(orders))
	if remaining < 0 {
		remaining = 0
	}
	s.logger.Debug("s.orderRepo.ListWithFilters",
		zap.Any("total", total),
		zap.Any("len(orders)", len(orders)),
		zap.Any("remaining", remaining),
		zap.Any("pagination.Offset", pagination.Offset),
		zap.Any("orders", orders))

	userOrders := make([]*domain.UserOrderResponse, 0)
	for _, order := range orders {
		uo := &domain.UserOrderResponse{
			OrderID:      order.OrderID,
			ProductID:    order.ProductID,
			ProductDesc:  order.ProductDesc,
			Quantity:     order.Quantity,
			Amount:       order.Amount,
			NetAmount:    order.NetAmount,
			Currency:     order.Currency,
			PayStatus:    order.PayStatus,
			PayedMethod:  order.PayedMethod,
			PSPProvider:  order.PSPProvider,
			CardNumber:   order.CardNumber,
			PayedAt:      order.PayedAt,
			RefundStatus: order.RefundStatus,
			RefundedAt:   order.RefundedAt}
		userOrders = append(userOrders, uo)
	}
	// 构建响应
	response := &domain.GetUserOrdersResponse{
		UserOrders: userOrders,
		Pagination: &domain.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}

	s.logger.Info("Successfully GetUserOrders",
		zap.Int("count", len(orders)),
		zap.Int64("total", total),
		zap.Int64("remaining", remaining))

	return response, nil
}

// ListAllOrders 获取所有订单列表（管理员接口）
func (s *orderService) ListAllOrders(filter *domain.OrderFilter, pagination *domain.PaginationRequest) (*domain.ListOrdersResponse, error) {
	s.logger.Info("orderService::ListAllOrders",
		zap.Any("filter", filter),
		zap.Any("pagination", pagination))

	// 调用仓储层获取数据
	orders, total, err := s.orderRepo.ListWithFilters(filter, pagination)
	if err != nil {
		s.logger.Error("Failed to list orders with filters",
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))
		return nil, err
	}

	// 计算剩余记录数
	remaining := total - int64(pagination.Offset+len(orders))
	if remaining < 0 {
		remaining = 0
	}
	s.logger.Debug("s.orderRepo.ListWithFilters",
		zap.Any("total", total),
		zap.Any("len(orders)", len(orders)),
		zap.Any("remaining", remaining),
		zap.Any("pagination.Offset", pagination.Offset),
		zap.Any("orders", orders))

	// 构建响应
	response := &domain.ListOrdersResponse{
		Orders: orders,
		Pagination: &domain.PaginationResponse{
			Total:     total,
			Limit:     pagination.Limit,
			Offset:    pagination.Offset,
			Remaining: remaining,
		},
	}

	s.logger.Info("Successfully listed orders",
		zap.Int("count", len(orders)),
		zap.Int64("total", total),
		zap.Int64("remaining", remaining))

	return response, nil
}

// UpdateOrder 更新订单
func (s *orderService) UpdateOrder(orderID string, req *domain.UpdateOrderRequest) error {
	s.logger.Info("Updating order", zap.String("order_id", orderID))

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取现有订单（使用行锁防止并发修改）
		order, err := s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			s.logger.Warn("UpdateOrder",
				logger.String("s.orderRepo.GetByOrderIDWithTransaction error", err.Error()))
			return err
		}
		s.logger.Debug("UpdateOrder", logger.Object("order", order))

		// 更新字段
		if req.PayStatus != "" {
			order.PayStatus = req.PayStatus
		}
		if req.PayedMethod != "" {
			order.PayedMethod = req.PayedMethod
		}
		if req.CardNumber != "" {
			order.CardNumber = req.CardNumber
		}
		if req.PayedAt != nil {
			order.PayedAt = req.PayedAt
		}
		if req.RefundStatus != "" {
			order.RefundStatus = req.RefundStatus
		}
		if req.RefundedAt != nil {
			order.RefundedAt = req.RefundedAt
		}
		if req.PSPPaymentID != "" {
			order.PSPPaymentID = req.PSPPaymentID
		}
		if req.PSPCustomerID != "" {
			order.PSPCustomerID = req.PSPCustomerID
		}
		if req.PSPCustomerEmail != "" {
			order.PSPCustomerEmail = req.PSPCustomerEmail
		}
		if req.PSPSubscriptionID != "" {
			order.PSPSubscriptionID = req.PSPSubscriptionID
		}
		if req.NetAmount != nil {
			order.NetAmount = *req.NetAmount
		}

		now := time.Now()
		order.UpdatedAt = &now

		// 在事务中保存更新
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to update order", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to update order: %w", err)
	}

	s.logger.Info("Order updated successfully", zap.String("order_id", orderID))
	return nil
}

// ProcessWebhook 处理支付网关的webhook
// 这里不确定 MQ 缓存事件是否必须。简化起见，先并发处理。
func (s *orderService) ProcessWebhook(provider string, header map[string][]string, payload []byte) error {
	s.logger.Info("Processing webhook", zap.String("provider", provider))

	// 获取支付网关
	gateway, exists := s.gateways[provider]
	if !exists {
		s.logger.Warn("ProcessWebhook",
			logger.String("payment gateway not supported", provider))
		return fmt.Errorf("payment gateway [%s] not supported", provider)
	}

	if err := gateway.Webhook(header, payload, s.paymentEventCb); err != nil {
		s.logger.Error("Failed to call webhook", zap.Error(err))
		return err
	}
	s.logger.Info("Webhook processed successfully", zap.String("provider", provider))
	return nil
}

// CancelOrder 取消订单
func (s *orderService) CancelOrder(orderID string) error {
	s.logger.Info("Cancelling order", zap.String("order_id", orderID))

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取订单（使用行锁防止并发修改）
		order, err := s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			s.logger.Warn("CancelOrder",
				logger.String("s.orderRepo.GetByOrderIDWithTransaction error", err.Error()))
			return err
		}
		s.logger.Debug("CancelOrder", logger.Object("order", order))

		if !order.IsCancellable() {
			return fmt.Errorf("order %s cannot be cancelled, current status: %s", orderID, order.PayStatus)
		}

		order.MarkAsCancelled()

		// 在事务中更新订单状态
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to cancel order", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	s.logger.Info("Order cancelled successfully", zap.String("order_id", orderID))
	return nil
}

// RefundOrder 订单退款
func (s *orderService) RefundOrder(requestContext *middleware.RequestContext,
	orderID string, amount *float64) error {

	s.logger.Info("Processing refund for order",
		zap.String("order_id", orderID),
		zap.Float64p("amount", amount))

	var refundAmount float64
	var order *domain.Order

	err := db.WithTransaction(func(tx *gorm.DB) error {
		// 在事务中获取订单（使用行锁防止并发修改）
		var err error
		order, err = s.orderRepo.GetByOrderIDWithTransaction(tx, orderID)
		if err != nil {
			s.logger.Warn("RefundOrder",
				logger.String("s.orderRepo.GetByOrderIDWithTransaction error", err.Error()))
			return err
		}
		s.logger.Debug("RefundOrder", logger.Object("order", order))

		if !order.IsRefundable() {
			return fmt.Errorf("order %s cannot be refunded, current status: %s, refund status: %s",
				orderID, order.PayStatus, order.RefundStatus)
		}

		// 如果没有指定退款金额，则全额退款
		refundAmount = order.Amount
		if amount != nil {
			refundAmount = *amount
		}

		// 调用支付网关进行退款
		gateway, exists := s.gateways[order.PSPProvider]
		if !exists {
			s.logger.Warn("RefundOrder",
				logger.String("payment gateway not supported", order.PSPProvider))
			return fmt.Errorf("payment gateway [%s] not supported", order.PSPProvider)
		}

		traceId := uuid.New().String()
		if requestContext != nil {
			traceId = requestContext.TraceId
		}
		refundID, err := gateway.RefundPayment(orderID,
			traceId, order.PSPPaymentID, order.PSPPaymentIntentID, refundAmount)
		if err != nil {
			s.logger.Error("Failed to refund payment",
				zap.String("order_id", orderID),
				zap.String("psp_payment_id", order.PSPPaymentID),
				zap.Error(err))
			return fmt.Errorf("failed to refund payment: %w", err)
		}
		s.logger.Info("Payment refunded successfully",
			zap.String("order_id", orderID),
			zap.String("psp_payment_id", order.PSPPaymentID),
			zap.String("refund_id", refundID))

		// 更新订单状态
		order.MarkAsRefundedRequested(refundAmount)
		order.PSPPaymentRefundID = refundID

		// 在事务中更新订单状态
		return s.orderRepo.UpdateWithTransaction(tx, order)
	})

	if err != nil {
		s.logger.Error("Failed to process refund", zap.String("order_id", orderID), zap.Error(err))
		return fmt.Errorf("failed to process refund: %w", err)
	}

	s.logger.Info("Order refunded successfully",
		zap.String("order_id", orderID),
		zap.Float64("refund_amount", refundAmount))
	return nil
}

// 订单支付事件处理
func (s *orderService) paymentEventCb(params *payment.EventCallBackParams) error {
	s.logger.Info("paymentEventCb",
		zap.Any("params", params))
	if len(params.Country) < 1 {
		params.Country = "USA"
	}
	if len(params.OrderID) < 1 {
		return fmt.Errorf("params.OrderID is empty")
	}

	switch params.Event {
	case payment.PaymentEventPending:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx,
				params.OrderID, domain.PayStatusPaid, params.Msg)
		},
		); err != nil {
			return err
		}

	case payment.PaymentEventSucceeded:

		err := s.updatePayStatusSucceeded(params)
		if err != nil {
			s.logger.Error("Failed to updatePayStatusSucceeded", zap.String("params.OrderID", params.OrderID), zap.Error(err))
			return fmt.Errorf("failed to updatePayStatusSucceeded(OrderID:%v): %w", params.OrderID, err)
		}

		err = s.updatePayStatusFulfilled(params)
		if err != nil {
			s.logger.Error("Failed to updatePayStatusFulfilled", zap.String("params.OrderID", params.OrderID), zap.Error(err))
			return fmt.Errorf("failed to updatePayStatusFulfilled(OrderID:%v): %w", params.OrderID, err)
		}

		// 最后获取订单状态记录到日志备查.
		ord, err := s.orderRepo.GetByOrderID(params.OrderID)
		if err != nil {
			s.logger.Error("failed to GetByOrderID", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("failed to GetByOrderID(OrderID:%v): %w", params.OrderID, err)
		}
		s.logger.Info("order processed successfully",
			logger.Object("order", ord))

	case payment.PaymentEventCanceled:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx,
				params.OrderID, domain.PayStatusCancelled, params.Msg)
		},
		); err != nil {
			return err
		}

	case payment.PaymentEventFailed:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx,
				params.OrderID, domain.PayStatusFailed, params.Msg)
		},
		); err != nil {
			return err
		}

	case payment.PaymentEventExpired:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateStatusOtherWithTransaction(tx,
				params.OrderID, domain.PayStatusExpired, params.Msg)
		},
		); err != nil {
			return err
		}

	case payment.RefundEventSucceeded:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateRefundSucceededWithTransaction(tx,
				params.OrderID, domain.RefundStatusSucceeded, params.Msg)
		},
		); err != nil {
			return err
		}

	case payment.RefundEventFailed:
		if err := db.WithTransaction(func(tx *gorm.DB) error {
			return s.orderRepo.UpdateRefundStatusFailedWithTransaction(tx,
				params.OrderID, domain.RefundStatusFailed, params.Msg)
		},
		); err != nil {
			return err
		}

	}

	return nil
}

func (s *orderService) updatePayStatusFulfilled(params *payment.EventCallBackParams) error {

	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 如果已经履约要避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.PayStatus == domain.PayStatusFulfilled {
			s.logger.Info("already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("order %v already Fulfilled before", params.OrderID)
		}

		// 履约前先处理参数
		var entitlementInt int64
		if len(params.Entitlement) > 0 {
			v, err := strconv.ParseInt(params.Entitlement, 10, 32)
			if err != nil {
				s.logger.Error("Failed to ParseInt", zap.String("params.OrderID", params.OrderID), zap.Error(err))
				return fmt.Errorf("failed to ParseInt(OrderID:%v): %w", params.OrderID, err)
			}
			entitlementInt = v
		}
		// TODO: 测试
		// if entitlementInt <= 0 {
		// 	entitlementInt = 12
		// }

		ctx := s.pointService.WithUserContext(context.Background(),
			params.UserID, params.Country, params.TraceID)

		s.logger.Info("pointService.WithUserMetadata",
			zap.Any("ctx", ctx),
			zap.String("params.UserID", params.UserID),
			zap.String("params.Country", params.Country),
			zap.String("params.TraceID", params.TraceID),
			zap.Float32("points", float32(entitlementInt)),
			zap.String("params.OrderID", params.OrderID),
		)

		err = s.pointService.ChargePoints(ctx, float32(entitlementInt), params.OrderID)
		if err != nil {
			s.logger.Error("Failed to ChargePoints", zap.String("params.OrderID", params.OrderID), zap.Error(err))
			return fmt.Errorf("failed to ChargePoints(OrderID:%v): %w", params.OrderID, err)
		}

		// 履约之后再更新订单状态
		return s.orderRepo.UpdateStatusSucceededWithTransaction(tx,
			params.OrderID, domain.PayStatusFulfilled, params.Msg)
	},
	); err != nil {
		return err
	}
	return nil
}

func (s *orderService) updatePayStatusSucceeded(params *payment.EventCallBackParams) error {
	if err := db.WithTransaction(func(tx *gorm.DB) error {
		// 先获取订单状态, 避免重复履约.
		ord, err := s.orderRepo.GetByOrderIDForUpdate(tx, params.OrderID)
		if err != nil {
			s.logger.Error("failed to GetByOrderIDForUpdate", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("failed to GetByOrderIDForUpdate(OrderID:%v): %w", params.OrderID, err)
		}
		if ord.PayStatus == domain.PayStatusFulfilled {
			s.logger.Info("already processed Fulfilled before", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("order %v already Fulfilled before", params.OrderID)
		}
		if ord.PayStatus != domain.PayStatusCreated && ord.PayStatus != domain.PayStatusPaid {
			s.logger.Warn("order %v status error", zap.String("params.OrderID", params.OrderID))
			return fmt.Errorf("order %v status error", params.OrderID)
		}

		// 再更新订单状态为付款成功待履约
		err = s.orderRepo.UpdateStatusSucceededWithTransaction(tx,
			params.OrderID, domain.PayStatusSucceeded, params.Msg)
		return err
	}); err != nil {
		return err
	}
	return nil
}
