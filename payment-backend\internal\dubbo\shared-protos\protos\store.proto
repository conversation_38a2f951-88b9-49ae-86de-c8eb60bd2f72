syntax = "proto3";

package com.aibook.storepb.grpc;
option java_multiple_files = true;
option go_package = "./storepb;storepb";

import "google/protobuf/timestamp.proto";

// 流量包信息
message Package {
  uint64 id = 1; // 自增主键 ID
  string package_id = 2; // 流量包ID (UUID v4)
  string package_name = 3; // 流量包名称
  string package_desc = 4; // 流量包描述
  string package_type = 5; // 流量包类型.目前支持 "traffic" 类型.
  int32 entitlement = 6; // 购买后获得的权益
  string entitlement_desc = 7; // 购买后获得的权益描述
  double original_price = 8; // 流量包原价
  optional double discount_price = 9; // 流量包优惠价，可为空
  optional google.protobuf.Timestamp discount_start_time = 10; // 优惠开始时间
  optional google.protobuf.Timestamp discount_end_time = 11; // 优惠结束时间
  string discount_desc = 12; // 优惠活动描述
  string sale_status = 13; // 出售状态 (on_sale/off_sale)
  string currency = 14; // 货币单位
  string country = 15; // 国家
  string psp_product_id = 16; // PSP产品ID
  string psp_product_desc = 17; // PSP产品描述
  string psp_price_id = 18; // PSP价格ID
  string psp_price_desc = 19; // PSP价格描述
  string product_img = 20; // 产品图片
  string extra1 = 21; // 附件字段1
  string extra2 = 22; // 附件字段2
  int32 extra3 = 23; // 附件字段3
  int32 extra4 = 24; // 附件字段4
  google.protobuf.Timestamp created_at = 25; // 创建时间
  google.protobuf.Timestamp updated_at = 26; // 更新时间
  bool deleted = 27; // 是否被逻辑删除
  optional google.protobuf.Timestamp deleted_at = 28; // 删除时间（逻辑删除）
}

// 流量包响应（终端用户）
message PackageResponse {
  string package_id = 1;
  string package_name = 2;
  string package_desc = 3;
  string package_type = 4;
  int32 entitlement = 5;
  string entitlement_desc = 6; // 购买后获得的权益描述
  double price = 7; // 实际价格（优惠价或原价）
  optional double original_price = 8; // 原价（仅在有优惠时显示）
  optional google.protobuf.Timestamp discount_start_time = 9; // 优惠开始时间
  optional google.protobuf.Timestamp discount_end_time = 10; // 优惠结束时间
  string discount_desc = 11; // 优惠活动描述
  string sale_status = 12; // 出售状态
  string currency = 13;
  string country = 14;
  string psp_product_id = 15; // PSP产品ID
  string psp_product_desc = 16; // PSP产品描述
  string psp_price_id = 17; // PSP价格ID
  string psp_price_desc = 18; // PSP价格描述
  string product_img = 19; // 产品图片
}

// 流量包响应（管理员）
message AdminPackageResponse {
  uint64 id = 1;
  string package_id = 2;
  string package_name = 3;
  string package_desc = 4;
  string package_type = 5;
  int32 entitlement = 6;
  string entitlement_desc = 7; // 购买后获得的权益描述
  double original_price = 8;
  optional double discount_price = 9;
  optional google.protobuf.Timestamp discount_start_time = 10;
  optional google.protobuf.Timestamp discount_end_time = 11;
  string discount_desc = 12; // 优惠活动描述
  string sale_status = 13;
  string currency = 14;
  string country = 15;
  string psp_product_id = 16; // PSP产品ID
  string psp_product_desc = 17; // PSP产品描述
  string psp_price_id = 18; // PSP价格ID
  string psp_price_desc = 19; // PSP价格描述
  string product_img = 20; // 产品图片
  string extra1 = 21;
  string extra2 = 22;
  int32 extra3 = 23;
  int32 extra4 = 24;
  google.protobuf.Timestamp created_at = 25;
  google.protobuf.Timestamp updated_at = 26;
}

// 流量包过滤条件
message PackageFilter {
  optional string currency = 1; // 过滤：货币单位
  optional string country = 2; // 过滤：国家
}

// 分页请求
message PaginationRequest {
  int32 limit = 1; // 每页数量
  int32 offset = 2; // 偏移量
}

// 分页响应
message PaginationResponse {
  int64 total = 1; // 总记录数
  int32 limit = 2; // 当前页大小
  int32 offset = 3; // 当前偏移量
  int64 remaining = 4; // 剩余记录数
}

// 用户上下文
message UserContext {
  string user_id = 1; // 用户ID
  string role = 2; // 用户角色
}

// 获取所有流量包请求（终端用户）
message ListAllPackagesRequest {
  UserContext user_context = 1; // 用户上下文
  optional PackageFilter filter = 2; // 过滤条件
  optional PaginationRequest pagination = 3; // 分页参数
}

// 获取所有流量包响应（终端用户）
message ListAllPackagesResponse {
  repeated PackageResponse packages = 1; // 流量包列表
  PaginationResponse pagination = 2; // 分页响应信息
}

// 创建流量包请求
message CreatePackageRequest {
  string package_name = 1; // 流量包名称
  string package_desc = 2; // 流量包描述
  string package_type = 3; // 流量包类型
  int32 entitlement = 4; // 购买后获得的权益
  string entitlement_desc = 5; // 购买后获得的权益描述
  double original_price = 6; // 流量包原价
  optional double discount_price = 7; // 流量包优惠价
  optional google.protobuf.Timestamp discount_start_time = 8; // 优惠开始时间
  optional google.protobuf.Timestamp discount_end_time = 9; // 优惠结束时间
  string discount_desc = 10; // 优惠活动描述
  string sale_status = 11; // 出售状态
  string currency = 12; // 货币单位
  string country = 13; // 国家
  string psp_product_id = 14; // PSP产品ID
  string psp_product_desc = 15; // PSP产品描述
  string psp_price_id = 16; // PSP价格ID
  string psp_price_desc = 17; // PSP价格描述
  string product_img = 18; // 产品图片
  string extra1 = 19; // 附件字段1
  string extra2 = 20; // 附件字段2
  int32 extra3 = 21; // 附件字段3
  int32 extra4 = 22; // 附件字段4
}

// 更新流量包请求
message UpdatePackageRequest {
  string package_id = 1; // 流量包ID
  optional string package_name = 2; // 流量包名称
  optional string package_desc = 3; // 流量包描述. 目前支持 "traffic" 类型.
  optional string package_type = 4; // 流量包类型
  optional int32 entitlement = 5; // 购买后获得的权益
  optional string entitlement_desc = 6; // 购买后获得的权益描述
  optional double original_price = 7; // 流量包原价
  optional double discount_price = 8; // 流量包优惠价
  optional google.protobuf.Timestamp discount_start_time = 9; // 优惠开始时间
  optional google.protobuf.Timestamp discount_end_time = 10; // 优惠结束时间
  optional string discount_desc = 11; // 优惠活动描述
  optional string sale_status = 12; // 出售状态 on_sale(起售), off_sale(停售)
  optional string currency = 13; // 货币单位
  optional string country = 14; // 国家
  optional string psp_product_id = 15; // PSP产品ID
  optional string psp_product_desc = 16; // PSP产品描述
  optional string psp_price_id = 17; // PSP价格ID
  optional string psp_price_desc = 18; // PSP价格描述
  optional string product_img = 19; // 产品图片
  optional string extra1 = 20; // 附件字段1. 暂未使用.
  optional string extra2 = 21; // 附件字段2. 暂未使用.
  optional int32 extra3 = 22; // 附件字段3. 暂未使用.
  optional int32 extra4 = 23; // 附件字段4. 暂未使用.
}

// 更新流量包响应
message UpdatePackageResponse {
  string message = 1; // 响应消息
}

// 删除流量包请求
message DeletePackageRequest {
  string package_id = 1; // 流量包ID
}

// 删除流量包响应
message DeletePackageResponse {
  string message = 1; // 响应消息
}

// 获取所有流量包请求（管理员）
message AdminListAllPackagesRequest {
  optional PackageFilter filter = 1; // 过滤条件
  optional PaginationRequest pagination = 2; // 分页参数
}

// 获取所有流量包响应（管理员）
message AdminListAllPackagesResponse {
  repeated AdminPackageResponse packages = 1; // 流量包列表
  PaginationResponse pagination = 2; // 分页响应信息
}

// 流量包服务定义
service StoreService {
  // 获取所有流量包列表（终端用户接口）
  rpc ListAllPackages(ListAllPackagesRequest) returns (ListAllPackagesResponse);
  
  // 添加流量包（管理员接口）
  rpc AdminAddPackages(CreatePackageRequest) returns (AdminPackageResponse);
  
  // 删除流量包（管理员接口）
  rpc AdminDeletePackages(DeletePackageRequest) returns (DeletePackageResponse);
  
  // 更新流量包（管理员接口）
  rpc AdminUpdatePackages(UpdatePackageRequest) returns (UpdatePackageResponse);
  
  // 获取所有流量包列表（管理员接口）
  rpc AdminListAllPackages(AdminListAllPackagesRequest) returns (AdminListAllPackagesResponse);
}
