package middleware

import (
	"errors"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// RequestContext 请求上下文信息
type RequestContext struct {
	TraceId string `json:"trace-id"`
}

const (
	// RequestContext 请求上下文键
	RequestContextKey string = "request_context"
)

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("x-trace-id")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.<PERSON>er("x-trace-id", requestID)
		c.Set("x-trace-id", requestID)

		requestContext := &RequestContext{
			TraceId: requestID,
		}

		// 将请求上下文存储到 Gin 上下文中
		c.Set(RequestContextKey, requestContext)

		c.Next()
	}
}

// RequireRequestIDMiddleware 强制要求客户端传 X-Request-ID
func RequireRequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID1 := c.GetHeader("x-trace-id")
		requestID2 := c.GetHeader("X-Request-ID")
		if requestID1 == "" && requestID2 == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "unauthorized",
				"message": "x-trace-id header is required",
			})
			c.Abort()
			return
		}
		requestID := requestID1
		if requestID == "" {
			requestID = requestID2
		}

		// 允许后续中间件继续使用它
		c.Set("x-trace-id", requestID)

		requestContext := &RequestContext{
			TraceId: requestID,
		}

		// 将请求上下文存储到 Gin 上下文中
		c.Set(RequestContextKey, requestContext)

		c.Next()
	}
}

// GetRequestContext 从 Gin 上下文中获取请求上下文
func GetRequestContext(c *gin.Context) (*RequestContext, bool) {
	value, exists := c.Get(string(RequestContextKey))
	if !exists {
		return nil, false
	}

	requestContext, ok := value.(*RequestContext)
	return requestContext, ok
}

// MustRequestContext 从 Gin 上下文中获取请求上下文（必须存在）
func MustGetRequestContext(c *gin.Context) (*RequestContext, error) {
	requestContext, exists := GetRequestContext(c)
	if !exists {
		return nil, errors.New("request context not found in gin context")
	}
	return requestContext, nil
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return uuid.New().String() // 使用 UUID 作为请求ID
}
