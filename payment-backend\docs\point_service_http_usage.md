# PointServiceHTTP 使用文档

## 概述

`PointServiceHTTP` 是一个通过 HTTP 协议调用用户服务的积分充值服务。它通过 Nacos 配置中心获取用户服务的 HTTP 地址，然后发送 HTTP 请求来调用 `ChargePoints` 接口。

## 功能特性

- 通过 HTTP 协议调用远程用户服务
- 从 Nacos 配置中心获取用户服务地址
- 自动参数验证
- 懒加载客户端初始化
- 完整的错误处理和日志记录
- 符合项目整体架构的依赖注入

## 接口定义

```go
type PointServiceHTTP interface {
    WithUserContext(ctx context.Context, userId string, country, traceId string) context.Context
    // ChargePoints 通过HTTP调用用户服务充值积分
    ChargePoints(ctx context.Context, points float32, reason string) error
}
```

## 配置要求

### 1. Nacos 配置中心

服务依赖 Nacos 配置中心来获取用户服务的 HTTP 地址：

```yaml
# config.yaml 或 config.dev.yaml
nacos:
  enabled: true
  endpoints:
    - "127.0.0.1:8848"
  namespace: "payment-service"
  username: "nacos"
  password: "nacos"
  config:
    enabled: true
    data_id: "user-service-config"
    group: "DEFAULT_GROUP"
```

### 2. 用户服务配置

在 Nacos 配置中心中需要配置用户服务的 HTTP 地址：

```yaml
# 在 Nacos 配置中心的 user-service-config 配置项中
http:
  base_url: "http://user-service:8081"
```

## HTTP 接口规范

根据 protobuf 定义，用户服务的 ChargePoints 接口：

- **URL**: `POST /api/v1/user/charge`
- **Content-Type**: `application/json`
- **请求头**:
  - `x-user-id`: 用户ID
  - `x-country`: 用户国家（可选）
  - `x-trace-id`: 追踪ID

**请求体**:
```json
{
  "points": 100.5,
  "reason": "订单支付奖励"
}
```

**响应**:
- **成功**: HTTP 200 OK
- **失败**: HTTP 4xx/5xx 错误码

## 使用示例

### 1. 在服务层中使用

```go
package service

import (
    "context"
    "payment-backend/internal/service"
)

type OrderService struct {
    pointServiceHTTP service.PointServiceHTTP
    // 其他依赖...
}

func (s *OrderService) ProcessPaymentSuccess(ctx context.Context, userID string, amount float32) error {
    // 计算奖励积分
    rewardPoints := amount * 0.01 // 1% 奖励
    
    // 创建用户上下文
    ctx = s.pointServiceHTTP.WithUserContext(ctx, userID, "US", "trace-123")
    
    // 充值积分
    if err := s.pointServiceHTTP.ChargePoints(ctx, rewardPoints, "支付成功奖励"); err != nil {
        return fmt.Errorf("failed to charge reward points: %w", err)
    }
    
    return nil
}
```

### 2. 在处理器中使用

```go
package handler

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "payment-backend/internal/service"
)

type AdminHandler struct {
    pointServiceHTTP service.PointServiceHTTP
}

func (h *AdminHandler) ChargeUserPoints(c *gin.Context) {
    var req struct {
        UserID string  `json:"user_id"`
        Points float32 `json:"points"`
        Reason string  `json:"reason"`
    }
    
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }
    
    // 创建用户上下文
    ctx := h.pointServiceHTTP.WithUserContext(c.Request.Context(), req.UserID, "US", "admin-charge")
    
    if err := h.pointServiceHTTP.ChargePoints(ctx, req.Points, req.Reason); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"status": "success"})
}
```

## 与 RPC 版本的对比

| 特性 | RPC 版本 (PointService) | HTTP 版本 (PointServiceHTTP) |
|------|------------------------|------------------------------|
| 协议 | Dubbo RPC (Triple) | HTTP/JSON |
| 服务发现 | Nacos 注册中心 | Nacos 配置中心 |
| 性能 | 更高 | 较低 |
| 调试 | 较难 | 容易 |
| 兼容性 | 需要 Dubbo 支持 | 标准 HTTP |
| 网络要求 | 内网 | 内网/外网 |

## 测试

### 1. 单元测试

```bash
go test ./internal/service -v -run TestPointServiceHTTP
```

### 2. 集成测试

```bash
cd test/testapi
go run . --test-charge-points-http
```

### 3. 手动测试

```bash
# 直接调用用户服务的 HTTP 接口
curl -X POST http://localhost:8081/api/v1/user/charge \
  -H "Content-Type: application/json" \
  -H "x-user-id: test_user_123" \
  -H "x-country: US" \
  -H "x-trace-id: test_trace_id" \
  -d '{
    "points": 100.5,
    "reason": "测试充值"
  }'
```

## 注意事项

### 1. 配置管理

- 确保 Nacos 配置中心中有正确的用户服务 HTTP 地址配置
- 在生产环境中，需要实现完整的 Nacos 配置读取逻辑

### 2. 错误处理

- HTTP 调用可能因为网络问题失败，需要适当的重试机制
- 需要处理用户服务返回的各种 HTTP 错误码

### 3. 性能考虑

- HTTP 调用比 RPC 调用有更高的延迟
- 考虑使用连接池来提高性能
- 可以实现缓存机制来减少配置中心的访问

### 4. 安全性

- 确保用户服务的 HTTP 接口有适当的认证和授权
- 在生产环境中使用 HTTPS
- 验证请求头中的用户信息

## 故障排查

### 1. 常见错误

- **"nacos is not enabled"**: 检查配置文件中的 `nacos.enabled` 设置
- **"failed to get user service base URL"**: 检查 Nacos 配置中心中的用户服务配置
- **HTTP 连接错误**: 检查用户服务是否正常运行，网络是否可达

### 2. 日志分析

查看日志中的关键信息：
- HTTP 请求 URL 和参数
- 响应状态码和内容
- 错误信息和堆栈跟踪

这个 HTTP 版本的积分服务提供了与 RPC 版本相同的功能，但使用更标准的 HTTP 协议，便于调试和跨语言集成。
